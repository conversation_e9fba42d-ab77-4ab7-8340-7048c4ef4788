/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import {Dimensions, FlatList, Pressable, Text, View} from 'react-native';
import {
  DefaultComment,
  SkeletonPlaceComment,
} from '../../Default/card/defauttComment';
import {AppButton, FBottomSheet} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {useCallback, useEffect, useRef, useMemo} from 'react';
import WScreenFooter from '../../../Screen/Layout/footer';
import {TextFieldForm} from '../../Default/form/component-form';
import {useForm} from 'react-hook-form';
import {AppDispatch} from '../../../redux/store/store';
import {useDispatch, useSelector} from 'react-redux';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {StorageContanst} from '../../../Config/Contanst';
import {postCommentsActions} from '../../community/reducers/postCommentsReducer';
import {createSelector} from '@reduxjs/toolkit';
import {navigate, RootScreen} from '../../../router/router';

// Memoized selector with safety checks
const selectPostComments = createSelector(
  [(state: any) => state, (_, postId: string) => postId],
  (state, postId) => {
    // Safety check: ensure postComments and byPostId exist
    const postCommentsState = state.postComments;
    if (!postCommentsState || !postCommentsState.byPostId) {
      return {
        comments: [],
        loading: false,
        error: null,
      };
    }

    const postData = postCommentsState.byPostId[postId];
    return {
      comments: postData?.data || [],
      loading: postData?.loading || false,
      error: postData?.error || null,
    };
  },
);

const usePostComments = (postId: string) => {
  return useSelector((state: any) => selectPostComments(state, postId));
};

export default function CommentsListNews({
  postId,
  onReply,
}: {
  postId: string;
  onReply?: (data: any) => void;
}) {
  const bottomSheetRef = useRef<any>(null);
  const dispatch: AppDispatch = useDispatch();
  const {comments, loading} = usePostComments(postId);
  const loadedRef = useRef(false);

  // Sử dụng useEffect với loadedRef để tránh load nhiều lần
  useEffect(() => {
    if (!loadedRef.current && postId) {
      dispatch(postCommentsActions.loadComments(postId));
      loadedRef.current = true;
    }
  }, [postId]);

  // Sử dụng useMemo để tránh tính toán lại rootComments mỗi khi render
  const rootComments = useMemo(
    () => comments.filter((comment: any) => !comment.ParentId),
    [comments],
  );

  // Tách renderItem ra để tránh tạo function mới mỗi lần render
  const renderComment = useCallback(
    ({item}: {item: any}) => (
      <DefaultComment
        onReply={onReply}
        onPressTitle={() => {
          navigate(RootScreen.ProfileCommunity, {Id: item.CustomerId});
        }}
        data={{
          ...item,
          relativeUser: {
            title: item.relativeUser?.title || 'Người dùng ITM',
            subtitle: item.relativeUser?.subtitle || 'Thời gian',
            ...item.relativeUser,
          },
        }}
        ListComment={comments.filter(
          (comment: any) => comment.ParentId === item.Id,
        )}
        mainContainerStyle={{
          borderBottomColor: ColorThemes.light.neutral_main_border_color,
          borderBottomWidth: 1,
        }}
        containerStyle={{
          paddingHorizontal: 16,
          paddingTop: 16,
          width: '100%',
        }}
      />
    ),
    [comments],
  );

  const keyExtractor = useCallback((item: any) => item.Id?.toString(), []);

  return (
    <View style={{flex: 1, width: Dimensions.get('window').width}}>
      <FBottomSheet ref={bottomSheetRef} />
      <FlatList
        nestedScrollEnabled
        scrollEnabled={false}
        data={rootComments}
        keyExtractor={keyExtractor}
        style={{width: '100%', height: '100%', flex: 1}}
        renderItem={renderComment}
        ListFooterComponent={() => <View style={{height: 100}} />}
        ListEmptyComponent={
          loading ? (
            <View style={{gap: 16}}>
              <SkeletonPlaceComment />
              <SkeletonPlaceComment />
              <SkeletonPlaceComment />
            </View>
          ) : (
            <View
              style={{
                height: 300,
                width: '100%',
                alignItems: 'center',
              }}>
              <Text
                style={{
                  ...TypoSkin.heading7,
                  textAlign: 'center',
                  paddingTop: 100,
                }}>
                Chưa có bình luận nào.
              </Text>
            </View>
          )
        }
      />
    </View>
  );
}

export const CommentsViewNewsFeed = ({
  height,
  parentId,
  postId,
}: {
  height?: number;
  parentId?: string;
  postId: string;
}) => {
  const methods = useForm<any>({shouldFocusError: false});
  const dispatch: AppDispatch = useDispatch();
  const {comments} = usePostComments(postId);

  const childComments = useMemo(
    () => comments.filter((comment: any) => comment.ParentId === parentId),
    [comments, parentId],
  );

  const handleAddComment = useCallback(async () => {
    const token = await getDataToAsyncStorage(StorageContanst.accessToken);
    if (!token) return;

    const commentText = methods.getValues().Comment;
    if (!commentText) return;
    await dispatch(
      postCommentsActions.addNewComment(postId, commentText, parentId),
    );
    methods.reset();
  }, [dispatch, methods, parentId, postId]);

  const handleToggleLike = useCallback(
    async (commentId: string, isCurrentlyLiked: boolean) => {
      const token = await getDataToAsyncStorage(StorageContanst.accessToken);
      if (!token) return;

      dispatch(
        postCommentsActions.toggleLike(postId, commentId, !isCurrentlyLiked),
      );
    },
    [dispatch, postId],
  );

  const renderChildComment = useCallback(
    ({item}: {item: any}) => (
      <DefaultComment
        data={item}
        onPressTitle={() => {
          navigate(RootScreen.ProfileCommunity, {Id: item.CustomerId});
        }}
        containerStyle={{padding: 16}}
        ListComment={comments?.filter(
          (comment: any) => comment.ParentId === item.Id,
        )}
        actionView={
          <View
            style={{
              flex: 1,
              flexDirection: 'row',
              alignItems: 'center',
            }}>
            <View
              style={{
                flex: 1,
                flexDirection: 'row',
                gap: 4,
                justifyContent: 'flex-start',
                alignItems: 'center',
              }}>
              <AppButton
                backgroundColor={'transparent'}
                borderColor="transparent"
                onPress={() => handleToggleLike(item.Id, item.IsLike)}
                containerStyle={{padding: 4}}
                title={`${item.Likes}`}
                textColor={
                  item.IsLike
                    ? ColorThemes.light.primary_main_color
                    : ColorThemes.light.neutral_text_subtitle_color
                }
                textStyle={TypoSkin.buttonText6}
                prefixIconSize={16}
                prefixIcon={
                  item.IsLike
                    ? 'fill/user interface/like'
                    : 'outline/user interface/like'
                }
              />
            </View>
          </View>
        }
      />
    ),
    [handleToggleLike],
  );

  return (
    <View
      style={{
        height: height ?? Dimensions.get('window').height / 1.66,
        width: '100%',
      }}>
      <Pressable style={{flex: 1, height: '100%'}}>
        <FlatList
          data={childComments}
          keyExtractor={item => item.Id?.toString()}
          style={{width: '100%', height: '100%'}}
          renderItem={renderChildComment}
          ListEmptyComponent={() => (
            <View
              style={{
                height: Dimensions.get('window').height / 1.66,
                width: '100%',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              <Text style={{...TypoSkin.heading7, textAlign: 'center'}}>
                Chưa có bình luận nào
              </Text>
            </View>
          )}
        />
      </Pressable>

      <WScreenFooter
        style={{
          flexDirection: 'row',
          gap: 8,
          paddingHorizontal: 16,
          paddingBottom: 16,
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <TextFieldForm
          textFieldStyle={{padding: 16}}
          style={{flex: 1}}
          register={methods.register}
          control={methods.control}
          errors={methods.formState.errors}
          placeholder="Write a comment"
          name="Comment"
        />
        <AppButton
          title={'Send'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            borderRadius: 8,
            paddingHorizontal: 12,
            height: 45,
          }}
          onPress={handleAddComment}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </View>
  );
};
