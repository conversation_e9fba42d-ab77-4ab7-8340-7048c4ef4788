/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {StyleSheet, View} from 'react-native';

import ReviewItem from '../../components/shop/ReviewItem';
import {InforHeader} from '../../Screen/Layout/headers/inforHeader';
const Review = () => {
  return (
    <View style={styles.container}>
      {/* Header */}

      <InforHeader title={'shop'} />
      <ReviewItem />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    maxHeight: 120,
  },

  navigator: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomColor: '#00FFFF',
    paddingBottom: 18,
    borderBottomWidth: 0.5,
  },
  orderInfo: {
    display: 'flex',
    marginLeft: 13,
  },
  title: {
    fontSize: 20,
  },
  numberOrder: {
    fontSize: 15,
    marginTop: 10,
    color: '#999',
  },
});

export default Review;
