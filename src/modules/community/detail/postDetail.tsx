import {
  Dimensions,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {ScrollView, FlatList} from 'react-native-gesture-handler';
import {
  Winicon,
  AppButton,
  SkeletonImage,
  ListTile,
  FDialog,
  FLoading,
  FBottomSheet,
  showBottomSheet,
  hideBottomSheet,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {navigate, navigateBack, RootScreen} from '../../../router/router';
import ConfigAPI from '../../../Config/ConfigAPI';
import CommentsListNews from '../../customer/listview/commentsNews';
import {useRoute} from '@react-navigation/native';
import HTMLProcessor from '../../../utils/convert';
import {onShare} from '../../../features/share';
import {useForm} from 'react-hook-form';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {newsFeedActions} from '../reducers/newsFeedReducer';
import store, {AppDispatch} from '../../../redux/store/store';
import {useDispatch} from 'react-redux';
import {SafeAreaView} from 'react-native-safe-area-context';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import React, {useCallback, useRef, useState} from 'react';
import {postCommentsActions} from '../reducers/postCommentsReducer';
import RenderHTML from 'react-native-render-html';
import FastImage from 'react-native-fast-image';
import {myFeedActions} from '../reducers/MyFeedReducer';
import {DataController} from '../../../base/baseController';
import EmptyPage from '../../../Screen/emptyPage';
import YouTubePlayer from '../../../utils/YouTubeWebViewPlayer';
import {extractVideoId} from '../card/defaultPost';
import ClickableImage from '../../../components/ClickableImage';
import {TextFieldForm} from '../../Default/form/component-form';

export default function PostDetail() {
  const route = useRoute<any>();
  const {item, forNew} = route.params;
  const [data, setItem] = useState<any>(item);
  const dispatch: AppDispatch = useDispatch();
  const scrollViewRef = useRef<ScrollView>(null);
  const bottomSheetRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  React.useEffect(() => {
    const getLikes = async () => {
      const likeController = new DataController('Like');
      const response = await likeController.getPatternList({
        query: forNew ? `@NewsId:{${data?.Id}}` : `@PostsId:{${data?.Id}}`,
        pattern: {
          CustomerId: ['Id', 'Name', 'AvatarUrl'],
        },
      });
      response.code === 200
        ? setItem((prevData: any) => {
            return {
              ...prevData,
              Likes: response.data,
              CustomerLiked: response.Customer,
            };
          })
        : [];
    };

    getLikes();
  }, [dispatch, data?.Id]);

  // Xử lý danh sách ảnh
  const imageList = React.useMemo(() => {
    if (!data?.Img) return [];

    // Nếu Img chứa dấu phẩy, tách thành mảng các ảnh
    if (data.Img.includes(',')) {
      return data.Img.split(',')
        .filter((img: string) => img && img.trim() !== '')
        .map((img: string) => ConfigAPI.urlImg + img.trim());
    }

    // Nếu chỉ có một ảnh
    return [ConfigAPI.urlImg + data.Img];
  }, [data?.Img]);

  // Cấu hình FastImage
  const fastImageProps = React.useMemo(
    () => ({
      priority: FastImage.priority.normal,
      cache: FastImage.cacheControl.immutable,
      resizeMode: FastImage.resizeMode.cover,
    }),
    [],
  );

  const methods = useForm<any>({
    shouldFocusError: false,
  });
  const user = useSelectorCustomerState().data;
  const dialogRef = useRef<any>(null);
  const handleToggleLike = useCallback(
    async (isCurrentlyLiked: boolean) => {
      if (!user) {
        return;
      }
      setItem((prevData: any) => {
        return {
          ...prevData,
          IsLike: !isCurrentlyLiked,
          Likes: isCurrentlyLiked ? prevData.Likes - 1 : prevData.Likes + 1,
        };
      });
      await dispatch(
        newsFeedActions.updateLike(
          data?.Id,
          isCurrentlyLiked,
          forNew ? true : false,
        ),
      );
      const newfeed = store
        .getState()
        .newsFeed.data.find((item: any) => item.Id === data?.Id);
      if (newfeed) {
        dispatch(myFeedActions.setLike(newfeed.Id, !isCurrentlyLiked));
      }
    },
    [dispatch, data?.Id],
  );

  const handleReply = useCallback(
    async (dataComment?: any) => {
      if (dataComment) {
        methods.setValue('Comment', undefined);
        methods.setValue('Comment', `@${dataComment.relativeUser.title} `);
        methods.setValue('CommentId', `${dataComment.Id}`);
        methods.setValue('UserComment', `${dataComment.relativeUser.title}`);
        // Focus the comment input field
      } else {
        methods.setValue('Comment', undefined);
        methods.setValue('CommentId', undefined);
        methods.setValue('UserComment', undefined);
      }
    },
    [methods],
  );

  const handleAddComment = async () => {
    if (user) {
      if (methods.getValues().Comment) {
        // setIsLoading(true);
        await dispatch(
          postCommentsActions.addNewComment(
            data?.Id,
            methods.getValues().Comment,
            methods.getValues().CommentId?.toString().trim() || undefined,
          ),
        );

        setItem((prevData: any) => {
          return {
            ...prevData,
            Comment: prevData.Comment + 1,
          };
        });

        if (
          methods.getValues().CommentId === undefined ||
          methods.getValues().CommentId === ''
        ) {
          scrollViewRef.current?.scrollToEnd({
            animated: true,
          });
        }

        methods.setValue('Comment', '');
        methods.setValue('CommentId', '');
        methods.setValue('UserComment', '');

        dispatch(newsFeedActions.updateCommentCount(data?.Id, 1));
        const newfeed = store
          .getState()
          .newsFeed.data.find((item: any) => item.Id === data?.Id);
        if (newfeed) {
          dispatch(myFeedActions.updateCommentCount(newfeed.Id, 1));
        }
        // setIsLoading(false);
      }
    } else {
      ///TODO: check chưa login thì confirm ra trang login
      dialogCheckAcc(dialogRef);
    }
  };
  return (
    <SafeAreaView
      edges={['top']}
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
      }}>
      <FDialog ref={dialogRef} />
      <FBottomSheet ref={bottomSheetRef} />
      <FLoading
        visible={isLoading}
        avt={require('../../../assets/appstore.png')}
      />
      <ListTile
        style={{paddingHorizontal: 16, padding: 0, paddingBottom: 16}}
        isClickLeading
        onPress={() => {
          if (!data?.relativeUser?.title && !data?.relativeUser?.image) return;
          navigate(RootScreen.ProfileCommunity, {Id: item.CustomerId});
        }}
        leading={
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <TouchableOpacity
              style={{
                paddingRight: 16,
                paddingVertical: 8,
              }}
              onPress={() => navigateBack()}>
              <Winicon src="outline/arrows/left-arrow" size={20} />
            </TouchableOpacity>
            {!data?.relativeUser?.title &&
            !data?.relativeUser?.image ? null : data?.relativeUser?.image ? (
              <FastImage
                key={data?.relativeUser?.image}
                source={{
                  uri: data?.relativeUser?.image.includes('https')
                    ? data?.relativeUser?.image
                    : ConfigAPI.urlImg + data?.relativeUser?.image,
                }}
                style={{
                  width: 48,
                  height: 48,
                  borderRadius: 50,
                  backgroundColor: '#f0f0f0',
                }}
              />
            ) : (
              <View
                style={{
                  width: 32,
                  height: 32,
                  borderRadius: 50,
                  backgroundColor: ColorThemes.light.Primary_Color_Main,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text
                  style={{
                    ...TypoSkin.heading7,
                    color: ColorThemes.light.Neutral_Background_Color_Absolute,
                  }}>
                  {data?.relativeUser?.title
                    ? data?.relativeUser?.title.charAt(0).toUpperCase()
                    : ''}
                </Text>
              </View>
            )}
          </View>
        }
        title={
          <Text
            style={{
              ...TypoSkin.heading7,
              color: ColorThemes.light.Neutral_Text_Color_Title,
            }}
            numberOfLines={1}>
            {data?.relativeUser?.title ?? ''}
          </Text>
        }
        subtitle={
          <Text
            onPress={() => {
              if (!data?.relativeUser?.title && !data?.relativeUser?.image)
                return;
              navigate(RootScreen.ProfileCommunity, {Id: item.CustomerId});
            }}
            style={{
              ...TypoSkin.subtitle3,
              color: ColorThemes.light.Neutral_Text_Color_Subtitle,
            }}
            numberOfLines={1}>
            {data?.relativeUser?.subtitle ?? ''}
          </Text>
        }
        subTitleStyle={{
          ...TypoSkin.subtitle3,
          color: ColorThemes.light.Neutral_Text_Color_Subtitle,
        }}
        trailing={
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 4,
            }}>
            {forNew ? null : (
              <AppButton
                backgroundColor={
                  ColorThemes.light.Neutral_Background_Color_Main
                }
                borderColor="transparent"
                onPress={async () => {
                  if (user) {
                    await dispatch(
                      newsFeedActions.addBookmark(
                        data?.Id,
                        data?.IsBookmark === true,
                      ),
                    );
                    const newfeed = store
                      .getState()
                      .newsFeed.data.find((item: any) => item.Id === data?.Id);
                    if (newfeed) {
                      dispatch(
                        myFeedActions.setBookmark(
                          newfeed.Id,
                          !data?.IsBookmark,
                        ),
                      );
                    }

                    // Cập nhật trạng thái bookmark trong state
                    setItem((prevData: any) => {
                      return {
                        ...prevData,
                        IsBookmark: !prevData.IsBookmark,
                      };
                    });
                  } else {
                    ///TODO: check chưa login thì confirm ra trang login
                    dialogCheckAcc(dialogRef);
                  }
                }}
                containerStyle={{
                  borderRadius: 100,
                  padding: 6,
                  height: 24,
                  width: 24,
                }}
                title={
                  <Winicon
                    src={
                      data?.IsBookmark === true
                        ? 'fill/user interface/bookmark'
                        : 'outline/user interface/bookmark'
                    }
                    size={14}
                    color={
                      data?.IsBookmark === true
                        ? ColorThemes.light.Warning_Color_Main
                        : ColorThemes.light.Neutral_Text_Color_Subtitle
                    }
                  />
                }
              />
            )}
            <AppButton
              backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
              borderColor="transparent"
              onPress={() => {
                showBottomSheet({
                  ref: bottomSheetRef,
                  title: 'Actions',
                  suffixAction: <View />,
                  prefixAction: (
                    <TouchableOpacity
                      onPress={() => hideBottomSheet(bottomSheetRef)}
                      style={{padding: 6, alignItems: 'center'}}>
                      <Winicon
                        src="outline/layout/xmark"
                        size={20}
                        color={ColorThemes.light.Neutral_Text_Color_Body}
                      />
                    </TouchableOpacity>
                  ),
                  children: (
                    <View
                      style={{
                        gap: 8,
                        height: Dimensions.get('window').height / 4,
                        width: '100%',
                        backgroundColor:
                          ColorThemes.light.Neutral_Background_Color_Absolute,
                      }}>
                      {/* <ListTile
                        onPress={() => {
                          hideBottomSheet(bottomSheetRef);

                          showSnackbar({
                            message: 'Chức năng đang được phát triển',
                            status: ComponentStatus.WARNING,
                          });
                        }}
                        title={'Report post'}
                        titleStyle={{...TypoSkin.body3}}
                      /> */}
                      {data.CustomerId === user.Id && (
                        <ListTile
                          onPress={() => {
                            hideBottomSheet(bottomSheetRef);

                            navigate(RootScreen.createPost, {
                              editPost: data,
                              groupId: null,
                            });
                          }}
                          title={'Edit post'}
                          titleStyle={{...TypoSkin.body3}}
                        />
                      )}
                      {data.CustomerId === user.Id && (
                        <ListTile
                          onPress={() => {
                            hideBottomSheet(bottomSheetRef);
                            dispatch(newsFeedActions.deletePost(data));
                          }}
                          title={'Delete post'}
                          titleStyle={{...TypoSkin.body3}}
                        />
                      )}
                    </View>
                  ),
                });
              }}
              containerStyle={{
                borderRadius: 100,
                padding: 6,
                height: 24,
                width: 24,
              }}
              title={
                <Winicon
                  src={'fill/user interface/menu-dots'}
                  size={14}
                  color={ColorThemes.light.Neutral_Text_Color_Subtitle}
                />
              }
            />
          </View>
        }
      />
      {/* contents */}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'height' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 0}
        // Add these props for smoother animation
        contentContainerStyle={{flex: 1}}
        style={{flex: 1}}>
        <ScrollView
          ref={scrollViewRef}
          style={{
            flex: 1,
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
          }}>
          {/* content */}
          <View style={{flex: 1, paddingHorizontal: 16}}>
            {data?.LinkVideo ? (
              <View style={{marginBottom: 16}}>
                <YouTubePlayer
                  videoId={extractVideoId(data?.LinkVideo) || ''}
                  height={200}
                  width={'100%'}
                  play={false}
                  useNativeControls={true}
                  playerParams={{
                    modestbranding: true,
                  }}
                />
              </View>
            ) : null}
            {/* img */}
            {data?.Content ? (
              <View style={{}}>
                {/* <Text
                  style={{
                    ...TypoSkin.body3,
                    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                    paddingBottom: 8,
                    paddingTop: 4,
                  }}>
                  {data?.Content.includes('<')
                    ? HTMLProcessor.processHTMLContent(data?.Content ?? '')
                    : data?.Content ?? ''}
                </Text> */}
                <RenderHTML
                  contentWidth={Dimensions.get('screen').width}
                  source={{html: data?.Content}}
                  tagsStyles={{
                    body: {margin: 0, padding: 0},
                    div: {margin: 0, padding: 0},
                    p: {margin: 0, marginBottom: 8},
                    b: {fontWeight: 'bold'},
                    i: {fontStyle: 'italic'},
                    u: {textDecorationLine: 'underline'},
                  }}
                />
              </View>
            ) : null}
            {imageList.length > 0 ? (
              imageList.length === 1 ? (
                // Trường hợp chỉ có 1 ảnh - giữ nguyên hiển thị như cũ
                <View
                  style={[
                    {
                      height: 234,
                      width: '100%',
                      marginTop: 4,
                    },
                  ]}>
                  <ClickableImage
                    source={{
                      uri:
                        imageList[0] ||
                        'https://placehold.co/234/FFFFFF/000000/png',
                      ...fastImageProps,
                    }}
                    style={{
                      width: '100%',
                      height: 234,
                      borderRadius: 8,
                    }}
                  />
                </View>
              ) : (
                // Trường hợp có nhiều ảnh - hiển thị theo chiều dọc
                <View>
                  <FlatList
                    data={imageList}
                    scrollEnabled={false}
                    renderItem={({item: imageUri}) => (
                      <View style={{marginTop: 4}}>
                        <ClickableImage
                          source={{
                            uri: imageUri,
                            ...fastImageProps,
                          }}
                          style={{
                            width: '100%',
                            height: 234,
                            borderRadius: 8,
                          }}
                        />
                      </View>
                    )}
                    keyExtractor={(item, index) => `image-${index}`}
                  />
                </View>
              )
            ) : null}
            {/* title */}
            {/* {data?.Name ? (
              <Text
                style={[
                  {
                    ...TypoSkin.heading7,
                    color: ColorThemes.light.Neutral_Text_Color_Title,
                  },
                  {
                    paddingBottom: data?.Description ? 4 : 0,
                  },
                ]}
                numberOfLines={2}>
                {data?.Name ?? ''}
              </Text>
            ) : null} */}
            {/* subtitle */}
            {/* {data?.Description ? (
              <View style={{paddingTop: 4, paddingBottom: 8}}>
                <Text
                  style={{
                    ...TypoSkin.subtitle3,
                    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                  }}>
                  {data?.Description ?? ''}
                </Text>
              </View>
            ) : null} */}
          </View>
          {/* actions */}
          <View
            style={{
              flexDirection: 'row',
              paddingTop: 16,
              paddingHorizontal: 16,
              alignItems: 'center',
              gap: 8,
              borderBottomWidth: 1,
              borderBottomColor: ColorThemes.light.Neutral_Border_Color_Main,
              paddingBottom: 16,
            }}>
            <AppButton
              backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
              borderColor="transparent"
              containerStyle={{
                padding: 4,
                height: 24,
                paddingVertical: 0,
                paddingHorizontal: 8,
              }}
              title={
                <Text
                  style={{
                    ...TypoSkin.buttonText5,
                    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                  }}>
                  {data?.Likes?.length ?? 0}
                </Text>
              }
              textColor={
                data?.IsLike === true
                  ? ColorThemes.light.Error_Color_Main
                  : ColorThemes.light.Neutral_Text_Color_Subtitle
              }
              onPress={() => handleToggleLike(data?.IsLike)}
              prefixIconSize={12}
              prefixIcon={
                data?.IsLike === true
                  ? 'fill/emoticons/heart'
                  : 'outline/emoticons/heart'
              }
            />
            <AppButton
              backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
              borderColor="transparent"
              containerStyle={{
                padding: 4,
                height: 24,
                paddingVertical: 0,
                paddingHorizontal: 8,
              }}
              prefixIcon={'outline/user interface/b-comment'}
              prefixIconSize={12}
              textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
              title={
                <Text
                  style={{
                    ...TypoSkin.buttonText5,
                    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                  }}>
                  {data?.Comment ?? 0}
                </Text>
              }
            />
            <AppButton
              backgroundColor={ColorThemes.light.Neutral_Background_Color_Main}
              borderColor="transparent"
              containerStyle={{
                padding: 4,
                height: 24,
                paddingVertical: 0,
                paddingHorizontal: 8,
              }}
              onPress={async () => {
                onShare({content: 'Hello world'});
              }}
              prefixIcon={'fill/arrows/social-sharing'}
              prefixIconSize={12}
              textColor={ColorThemes.light.Neutral_Text_Color_Subtitle}
            />
          </View>
          {/* likes */}
          {data?.Likes?.length > 0 ? (
            <TouchableOpacity
              onPress={() => {
                showBottomSheet({
                  ref: bottomSheetRef,
                  enableDismiss: true,
                  title: 'Likes',
                  children: (
                    <View
                      style={{
                        height: Dimensions.get('window').height / 1.66,
                        width: '100%',
                        backgroundColor:
                          ColorThemes.light.Neutral_Background_Color_Absolute,
                      }}>
                      <Pressable
                        style={{
                          flex: 1,
                          backgroundColor:
                            ColorThemes.light.Neutral_Background_Color_Absolute,
                        }}>
                        <FlatList
                          data={data?.CustomerLiked}
                          nestedScrollEnabled
                          style={{
                            height: '100%',
                            backgroundColor:
                              ColorThemes.light
                                .Neutral_Background_Color_Absolute,
                          }}
                          keyExtractor={(item, index) => index.toString()}
                          ListEmptyComponent={() => {
                            return <EmptyPage title="Không có dữ liệu" />;
                          }}
                          ListFooterComponent={() => {
                            return <View style={{height: 32}} />;
                          }}
                          renderItem={({item}) => {
                            return (
                              <ListTile
                                key={item?.Id}
                                onPress={() => {
                                  hideBottomSheet(bottomSheetRef);
                                  navigate(RootScreen.ProfileCommunity, {
                                    Id: item?.Id,
                                  });
                                }}
                                listtileStyle={{gap: 8}}
                                leading={
                                  <SkeletonImage
                                    source={{
                                      uri: item?.AvatarUrl
                                        ? `${
                                            ConfigAPI.urlImg + item?.AvatarUrl
                                          }`
                                        : 'https://placehold.co/48/FFFFFF/000000/png',
                                    }}
                                    style={{
                                      width: 48,
                                      height: 48,
                                      borderRadius: 50,
                                      backgroundColor: '#f0f0f0',
                                    }}
                                  />
                                }
                                title={
                                  <Text
                                    style={{
                                      ...TypoSkin.heading7,
                                      color:
                                        ColorThemes.light
                                          .Neutral_Text_Color_Title,
                                    }}
                                    numberOfLines={1}>
                                    {item?.Name}
                                  </Text>
                                }
                              />
                            );
                          }}
                        />
                      </Pressable>
                    </View>
                  ),
                });
              }}
              style={{padding: 16}}>
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                }}>
                {data?.CustomerLiked?.length == 1
                  ? `${data?.CustomerLiked[0].Name} đã thích bài viết này`
                  : `${data?.CustomerLiked?.length} người đã thích bài viết này`}
              </Text>
            </TouchableOpacity>
          ) : null}
          {/* comments */}
          <CommentsListNews postId={data?.Id} onReply={handleReply} />
        </ScrollView>
      </KeyboardAvoidingView>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
        }}>
        <View
          style={{
            backgroundColor:
              ColorThemes.light.Neutral_Background_Color_Absolute,
            paddingHorizontal: 16,
            marginBottom: 32,
            paddingTop: 8,
            borderTopWidth: 1,
            borderTopColor: ColorThemes.light.Neutral_Border_Color_Main,
          }}>
          {methods.watch('CommentId') ? (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                gap: 4,
              }}>
              <Text
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                }}>
                Reply to {methods.getValues().UserComment ?? ''}
              </Text>
              <Text
                onPress={() => {
                  methods.setValue('CommentId', undefined);
                  methods.setValue('Comment', undefined);
                  methods.setValue('UserComment', undefined);
                }}
                style={{
                  ...TypoSkin.body3,
                  color: ColorThemes.light.Neutral_Text_Color_Subtitle,
                }}>
                - Hủy
              </Text>
            </View>
          ) : null}
          <View
            style={{
              flexDirection: 'row',
              gap: 8,
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <TextFieldForm
              textFieldStyle={{
                padding: 16,
                height: 40,
                paddingVertical: 0,
              }}
              style={{
                flex: 1,
              }}
              register={methods.register}
              control={methods.control}
              errors={methods.formState.errors}
              placeholder="Viết bình luận của bạn"
              name="Comment"
            />
            <AppButton
              prefixIcon={'fill/user interface/send-message'}
              prefixIconSize={24}
              backgroundColor={ColorThemes.light.transparent}
              borderColor="transparent"
              containerStyle={{
                paddingHorizontal: 12,
                height: 45,
              }}
              onPress={handleAddComment}
              textColor={ColorThemes.light.Primary_Color_Main}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderBottomWidth: 0,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 18,
    fontWeight: 'bold',
  },

  tabBar: {
    flexDirection: 'row',
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Absolute,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginBottom: 8,
    height: 56,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 4,
    paddingHorizontal: 17,
    borderRadius: 20,
    borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
    borderWidth: 1,
  },
  activeTab: {
    backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
    borderWidth: 0,
  },
  tabText: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.Neutral_Text_Color_Subtitle,
    marginLeft: 4,
  },
  activeTabText: {
    color: '#333',
    fontWeight: 'bold',
  },
  postContainer: {
    backgroundColor: '#fff',
    marginBottom: 8,
  },
  defaultPostContainer: {
    paddingHorizontal: 0,
    marginBottom: 0,
  },
  postHeaderRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bookmarkButton: {
    marginRight: 16,
  },
  postFooter: {
    flexDirection: 'row',
    paddingTop: 16,
    alignItems: 'center',
    gap: 8,
  },
  footerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  footerButtonText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
});
