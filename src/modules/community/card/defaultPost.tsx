/* eslint-disable react-native/no-inline-styles */
import React, {useState, useCallback, useMemo, memo} from 'react';
import {
  Dimensions,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {
  AppButton,
  ListTile,
  SkeletonImage,
  Winicon,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import ConfigAPI from '../../../Config/ConfigAPI';
import RenderHTML from 'react-native-render-html';
// Sử dụng component YouTubePlayer tùy chỉnh thay vì component mặc định
import YouTubePlayer from '../../../utils/YouTubeWebViewPlayer';
import ClickableImage from '../../../components/ClickableImage';
// Data structure is defined in Props interface

interface Props {
  containerStyle?: ViewStyle;
  mainContainerStyle?: ViewStyle;
  imgStyle?: ViewStyle;
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  data: any;
  listItems?: Array<any>;
  listTags?: Array<any>;
  onPressSeeMore?: () => void;
  onPressDetail?: () => void;
  onPressHeader?: () => void;
  reportContent?: React.ReactNode;
  actionView?: React.ReactNode;
  trailingView?: React.ReactNode;
  horizontalList?: boolean;
  noDivider?: boolean;
  showContent?: boolean;
  dividerColor?: string;
}

// Common image props để tránh lặp lại
const defaultImageProps = {
  priority: FastImage.priority.normal,
  cache: FastImage.cacheControl.immutable,
};

// Tách thành component riêng và sử dụng memo để tránh render lại
const SingleImage = memo(({imageUrl}: {imageUrl: string}) => (
  <ClickableImage
    source={{
      uri: imageUrl,
      ...defaultImageProps,
    }}
    style={imageStyles.fullImage}
    resizeMode="cover"
  />
  // <FastImage
  //   source={{
  //     uri: imageUrl,
  //     ...defaultImageProps,
  //   }}
  //   style={imageStyles.fullImage}
  //   resizeMode={FastImage.resizeMode.cover}
  // />
));

// Component cho 2 ảnh
const TwoImages = memo(({imageUrls}: {imageUrls: string[]}) => (
  <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
    <ClickableImage
      key="img-0"
      source={{
        uri: imageUrls[0],
        ...defaultImageProps,
      }}
      style={imageStyles.halfImage}
      resizeMode={FastImage.resizeMode.cover}
    />
    <ClickableImage
      key="img-1"
      source={{
        uri: imageUrls[1],
        ...defaultImageProps,
      }}
      style={imageStyles.halfImage}
      resizeMode={FastImage.resizeMode.cover}
    />
  </View>
));

// Component cho 3 ảnh
const ThreeImages = memo(({imageUrls}: {imageUrls: string[]}) => (
  <View>
    <ClickableImage
      source={{
        uri: imageUrls[0],
        ...defaultImageProps,
      }}
      style={imageStyles.topFullImage}
      resizeMode={FastImage.resizeMode.cover}
    />
    <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
      <ClickableImage
        key="img-1"
        source={{
          uri: imageUrls[1],
          ...defaultImageProps,
        }}
        style={imageStyles.halfSmallImage}
        resizeMode={FastImage.resizeMode.cover}
      />
      <ClickableImage
        key="img-2"
        source={{
          uri: imageUrls[2],
          ...defaultImageProps,
        }}
        style={imageStyles.halfSmallImage}
        resizeMode={FastImage.resizeMode.cover}
      />
    </View>
  </View>
));

// Component cho 4+ ảnh, sử dụng FastImage thay cho ImageBackground
const MultipleImages = memo(
  ({imageUrls, imageCount}: {imageUrls: string[]; imageCount: number}) => {
    // Nếu có đúng 4 ảnh, hiển thị 4 ảnh bằng nhau
    if (imageCount === 4) {
      return (
        <View
          style={{
            flexDirection: 'row',
            flexWrap: 'wrap',
            justifyContent: 'space-between',
          }}>
          <ClickableImage
            key="img-0"
            source={{
              uri: imageUrls[0],
              ...defaultImageProps,
            }}
            style={imageStyles.quadImage}
            resizeMode={FastImage.resizeMode.cover}
          />
          <ClickableImage
            key="img-1"
            source={{
              uri: imageUrls[1],
              ...defaultImageProps,
            }}
            style={imageStyles.quadImage}
            resizeMode={FastImage.resizeMode.cover}
          />
          <ClickableImage
            key="img-2"
            source={{
              uri: imageUrls[2],
              ...defaultImageProps,
            }}
            style={imageStyles.quadImageBottom}
            resizeMode={FastImage.resizeMode.cover}
          />
          <ClickableImage
            key="img-3"
            source={{
              uri: imageUrls[3],
              ...defaultImageProps,
            }}
            style={imageStyles.quadImageBottom}
            resizeMode={FastImage.resizeMode.cover}
          />
        </View>
      );
    }

    // Nếu có hơn 4 ảnh, hiển thị 4 ảnh với overlay ở ảnh cuối
    return (
      <View
        style={{
          flexDirection: 'row',
          flexWrap: 'wrap',
          justifyContent: 'space-between',
        }}>
        <FastImage
          key="img-0"
          source={{
            uri: imageUrls[0],
            ...defaultImageProps,
          }}
          style={imageStyles.quadImage}
          resizeMode={FastImage.resizeMode.cover}
        />
        <FastImage
          key="img-1"
          source={{
            uri: imageUrls[1],
            ...defaultImageProps,
          }}
          style={imageStyles.quadImage}
          resizeMode={FastImage.resizeMode.cover}
        />
        <FastImage
          key="img-2"
          source={{
            uri: imageUrls[2],
            ...defaultImageProps,
          }}
          style={imageStyles.quadImageBottom}
          resizeMode={FastImage.resizeMode.cover}
        />
        <View style={{position: 'relative', width: '49.5%', height: 116}}>
          <FastImage
            key="img-3"
            source={{
              uri: imageUrls[3],
              ...defaultImageProps,
            }}
            style={imageStyles.quadImageBottom}
            resizeMode={FastImage.resizeMode.cover}
          />
          {imageCount > 4 && (
            <View style={imageStyles.overlay}>
              <Text style={imageStyles.overlayText}>+{imageCount - 4}</Text>
            </View>
          )}
        </View>
      </View>
    );
  },
);

// Note: We've moved the RenderHTML config directly into the component props

// Sử dụng hàm này để cắt nội dung HTML an toàn
const truncateHtml = (html: string, maxLength: number): string => {
  if (html.length <= maxLength) return html;

  // Tìm vị trí kết thúc an toàn (tại một khoảng trắng)
  let safeEndIndex = maxLength;
  while (safeEndIndex > 0 && html[safeEndIndex] !== ' ') {
    safeEndIndex--;
  }

  if (safeEndIndex === 0) safeEndIndex = maxLength;

  // Kiểm tra các thẻ mở và đóng
  const openTags: string[] = [];
  const regex = /<\/?([a-z][a-z0-9]*)\b[^>]*>/gi;
  let match;
  let safeTruncatedHtml = html.substring(0, safeEndIndex);

  // Tìm tất cả các thẻ mở và đóng trong phần đã cắt
  while ((match = regex.exec(html.substring(0, safeEndIndex))) !== null) {
    if (match[0].indexOf('</') === 0) {
      // Thẻ đóng, xóa thẻ mở tương ứng khỏi stack
      const lastIndex = openTags.lastIndexOf(match[1]);
      if (lastIndex !== -1) {
        openTags.splice(lastIndex, 1);
      }
    } else if (match[0].indexOf('/>') === -1) {
      // Thẻ mở (không phải self-closing), thêm vào stack
      openTags.push(match[1]);
    }
  }

  // Đóng tất cả các thẻ còn mở
  for (let i = openTags.length - 1; i >= 0; i--) {
    safeTruncatedHtml += `</${openTags[i]}>`;
  }

  return safeTruncatedHtml;
};
export function extractVideoId(url: string): string | null {
  const match = url.match(
    /(?:youtube\.com\/.*v=|youtu\.be\/|embed\/)([a-zA-Z0-9_-]{11})/,
  );
  return match ? match[1] : null;
}
// Tối ưu component chính với React.memo
export const DefaultPost = memo((props: Props) => {
  const [isContentExpanded, setIsContentExpanded] = useState(false);
  const contentWidth = useMemo(() => Dimensions.get('window').width - 32, []);

  // Tối ưu xử lý ảnh với useMemo
  const imageData = useMemo(() => {
    if (!props?.data?.Img) return {hasImages: false};

    const imgStr = props.data.Img;
    if (imgStr.includes(',')) {
      const imgArray = imgStr
        .split(',')
        .filter((img: any) => img && img.trim() !== '')
        .slice(0, 10);

      if (imgArray.length === 0) return {hasImages: false};

      return {
        hasImages: true,
        isMultiple: true,
        imageUrls: imgArray.map((img: any) => `${ConfigAPI.urlImg}${img}`),
        count: imgArray.length,
      };
    } else {
      return {
        hasImages: true,
        isMultiple: false,
        imageUrls: [`${ConfigAPI.urlImg}${imgStr}`],
        count: 1,
      };
    }
  }, [props?.data?.Img]);

  // Tối ưu render ảnh với useMemo
  const renderedImages = useMemo(() => {
    if (!imageData.hasImages) return null;

    if (!imageData.isMultiple) {
      return <SingleImage imageUrl={imageData.imageUrls[0]} />;
    }

    switch (imageData.count) {
      case 1:
        return <SingleImage imageUrl={imageData.imageUrls[0]} />;
      case 2:
        return <TwoImages imageUrls={imageData.imageUrls} />;
      case 3:
        return <ThreeImages imageUrls={imageData.imageUrls} />;
      default:
        return (
          <MultipleImages
            imageUrls={imageData.imageUrls}
            imageCount={imageData.count}
          />
        );
    }
  }, [imageData]);

  // Tối ưu HTML content với cắt HTML an toàn
  const htmlSource = useMemo(() => {
    if (!props.data?.Content) return {html: ''};

    const content = props.data.Content;
    // Cắt HTML an toàn chỉ khi nội dung dài và chưa mở rộng
    const processedContent =
      !isContentExpanded && content.length > 500
        ? truncateHtml(content, 500)
        : content;

    return {html: processedContent};
  }, [props.data?.Content, isContentExpanded]);

  // Sử dụng useCallback để tránh tạo lại hàm mỗi khi render
  const toggleContentExpand = useCallback(() => {
    setIsContentExpanded(prev => !prev);
  }, []);

  // Sử dụng useMemo để tính toán isLongContent
  const isLongContent = useMemo(() => {
    return props.data?.Content && props.data.Content.length > 500;
  }, [props.data?.Content]);

  // Note: tagsStyles are now defined directly in the RenderHTML component

  // Tối ưu renderersProps
  const renderersProps = useMemo(
    () => ({
      img: {
        enableExperimentalPercentWidth: true,
      },
    }),
    [],
  );

  // Memoize compute embedded max width function
  const computeEmbeddedMaxWidth = useCallback(
    () => contentWidth - 32,
    [contentWidth],
  );

  // Tối ưu domVisitors thành một đối tượng tĩnh để tránh tạo mới mỗi lần render
  const domVisitors = useMemo(
    () => ({
      onElement: (element: any) => {
        // Loại bỏ các thuộc tính không cần thiết
        if (element.attribs && element.attribs.style) {
          // Giữ lại chỉ các style cần thiết
          const importantStyles = [
            'color',
            'font-size',
            'font-weight',
            'text-align',
          ];
          const styleStr = element.attribs.style;
          const styles = styleStr.split(';');
          const filteredStyles = styles.filter((style: any) => {
            const prop = style.split(':')[0]?.trim();
            return prop && importantStyles.includes(prop);
          });
          element.attribs.style = filteredStyles.join(';');
        }
        return element;
      },
    }),
    [],
  );

  // Render nội dung HTML với hiệu suất tối ưu
  const renderHtmlContent = useCallback(() => {
    if (!props.data?.Content) return null;

    return (
      <View style={contentStyles.contentContainer}>
        <RenderHTML
          contentWidth={contentWidth}
          source={htmlSource}
          tagsStyles={{
            body: {margin: 0, padding: 0},
            div: {margin: 0, padding: 0},
            p: {margin: 0, marginBottom: 8},
            b: {fontWeight: 'bold' as const},
            i: {fontStyle: 'italic'},
            u: {textDecorationLine: 'underline'},
          }}
          renderersProps={renderersProps}
          defaultTextProps={{
            selectable: false,
          }}
          enableExperimentalMarginCollapsing={true}
          enableCSSInlineProcessing={false}
          systemFonts={['System']}
          ignoredDomTags={['script', 'style', 'iframe']}
          computeEmbeddedMaxWidth={computeEmbeddedMaxWidth}
          domVisitors={domVisitors}
        />
        {isLongContent && !isContentExpanded && (
          <TouchableOpacity
            onPress={toggleContentExpand}
            style={contentStyles.readMoreButton}>
            <Text style={contentStyles.readMoreText}>Xem thêm</Text>
          </TouchableOpacity>
        )}
        {isLongContent && isContentExpanded && (
          <TouchableOpacity
            onPress={toggleContentExpand}
            style={contentStyles.readMoreButton}>
            <Text style={contentStyles.readMoreText}>Thu gọn</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }, [
    props.data?.Content,
    isContentExpanded,
    isLongContent,
    htmlSource,
    contentWidth,
    renderersProps,
    toggleContentExpand,
    computeEmbeddedMaxWidth,
    domVisitors,
  ]);

  // Tạo memoized user image uri
  const userImageUri = useMemo(() => {
    return props.data?.relativeUser?.image
      ? props.data?.relativeUser?.image.includes('http') ||
        props.data?.relativeUser?.image.includes('https')
        ? props.data?.relativeUser?.image
        : `${ConfigAPI.urlImg + props.data?.relativeUser?.image}`
      : 'https://placehold.co/48/FFFFFF/000000/png';
  }, [props.data?.relativeUser?.image]);

  return (
    <ListTile
      style={{flex: 1, height: '100%', borderRadius: 0}}
      leading={
        <FastImage
          key={userImageUri}
          source={{uri: userImageUri}}
          style={{
            width: 48,
            height: 48,
            borderRadius: 50,
            backgroundColor: '#f0f0f0',
          }}
        />
      }
      title={
        <Text
          style={{
            ...TypoSkin.heading7,
            color: ColorThemes.light.Neutral_Text_Color_Title,
          }}
          numberOfLines={1}>
          {props.data?.relativeUser?.title ?? '-'}
        </Text>
      }
      subtitle={props.data?.relativeUser?.subtitle ?? '-'}
      subTitleStyle={{
        ...TypoSkin.subtitle3,
        color: ColorThemes.light.Neutral_Text_Color_Subtitle,
      }}
      onPress={props.onPressHeader}
      disabled={!props.onPressHeader}
      trailing={props.trailingView}
      // content
      bottom={
        <View style={{flex: 1, width: '100%', paddingTop: 16}}>
          <View style={{flex: 1, marginTop: 4}}>
            {/* Video player - Tách ra khỏi TouchableOpacity để tránh trùng sự kiện */}
            {props.data?.LinkVideo ? (
              <View style={{marginBottom: 16}}>
                {/* <YoutubePlayer
                  height={200}
                  width={'100%'}
                  videoId={extractVideoId(props.data?.LinkVideo)}
                  play={false}

                /> */}
                <YouTubePlayer
                  videoId={extractVideoId(props.data?.LinkVideo) || ''}
                  height={200}
                  width={'100%'}
                  play={false}
                  useNativeControls={true}
                  playerParams={{
                    modestbranding: true,
                  }}
                />
              </View>
            ) : null}

            <TouchableOpacity
              onPress={props.onPressDetail}
              disabled={!props.onPressDetail}
              style={{flex: 1}}>
              {/* content */}
              {renderHtmlContent()}
              {/* img */}
              {imageData.hasImages ? (
                <View style={[stylesDefault.img, {marginVertical: 16}]}>
                  {renderedImages}
                </View>
              ) : null}

              {/* title */}
              {/* {props.data.Name ? (
                <Text
                  style={[
                    stylesDefault.titleStyle,
                    {
                      paddingBottom: props.data.Description ? 4 : 0,
                      ...props.titleStyle,
                    },
                  ]}
                  numberOfLines={2}>
                  {props.data.Name ?? ''}
                </Text>
              ) : null} */}

              {/* subtitle */}
              {/* {props.data.Description ? (
                <View style={{paddingTop: 4, paddingBottom: 8}}>
                  <Text
                    style={[stylesDefault.subTitleStyle, props.subtitleStyle]}
                    numberOfLines={2}>
                    {props.data.Description ?? ''}
                  </Text>
                </View>
              ) : null} */}

              {/* HTML content */}

              {/* List items */}
              {renderListItems(props)}

              {/* See more button */}
              {renderSeeMoreButton(props)}

              {/* Tags */}
              {renderTags(props)}
            </TouchableOpacity>
          </View>

          {/* actions */}
          {props.actionView && (
            <View style={{flexDirection: 'row'}}>{props.actionView}</View>
          )}
        </View>
      }
    />
  );
});

// Tách các phần render phụ ra thành các hàm riêng
const renderListItems = (props: Props) => {
  if (!props.listItems?.length) return null;

  return (
    <View style={{width: '100%', paddingTop: 16}}>
      {props.listItems.map(item => (
        <View
          key={item.Id}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            paddingVertical: 4,
            gap: 8,
          }}>
          <View style={{width: 16, borderRadius: 100}}>
            {item.icon ? (
              <Winicon src={item.icon} size={16} />
            ) : (
              <Text style={[stylesDefault.inforTitle]}>*</Text>
            )}
          </View>
          <Text style={[stylesDefault.inforTitle, {color: '#313135'}]}>
            {item.title}
          </Text>
        </View>
      ))}
    </View>
  );
};

const renderSeeMoreButton = (props: Props) => {
  if (!props.onPressSeeMore || !props.listItems?.length) return null;

  return (
    <AppButton
      title={'See more'}
      containerStyle={{
        justifyContent: 'flex-start',
        alignSelf: 'baseline',
        marginVertical: 8,
      }}
      backgroundColor={'transparent'}
      textStyle={TypoSkin.buttonText3}
      borderColor="transparent"
      suffixIconSize={16}
      suffixIcon={'outline/arrows/circle-arrow-right'}
      onPress={props.onPressSeeMore}
      textColor={ColorThemes.light.Info_Color_Main}
    />
  );
};

const renderTags = (props: Props) => {
  if (!props.listTags?.length) return null;

  return (
    <View style={{width: '100%', flexDirection: 'row', gap: 8}}>
      {props.listTags.map(item => (
        <View
          key={item.Id}
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-start',
            backgroundColor: ColorThemes.light.Neutral_Background_Color_Main,
            paddingHorizontal: 8,
            borderRadius: 24,
            borderColor: ColorThemes.light.Neutral_Border_Color_Bolder,
            borderWidth: 1,
            paddingVertical: 4,
            gap: 4,
          }}>
          <Text style={[stylesDefault.inforTitle]}>{item.title}</Text>
          <Winicon src={'outline/arrows/right-arrow'} size={12} />
        </View>
      ))}
    </View>
  );
};

export function SkeletonPlacePostCard() {
  return (
    <SkeletonPlaceholder backgroundColor="#f0f0f0" highlightColor="#e0e0e0">
      <View
        style={{
          padding: 16,
          gap: 12,
          backgroundColor: ColorThemes.light.white, // Add white background
        }}>
        {/* Header with avatar and name */}
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}>
          <View style={{flexDirection: 'row', alignItems: 'center', gap: 12}}>
            {/* Avatar */}
            <View
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
              }}
            />

            {/* Name and date */}
            <View style={{gap: 4}}>
              <View
                style={{
                  width: 150,
                  height: 16,
                  borderRadius: 4,
                }}
              />
              <View
                style={{
                  width: 100,
                  height: 12,
                  borderRadius: 4,
                }}
              />
            </View>
          </View>

          {/* Action buttons */}
          <View style={{flexDirection: 'row', gap: 8}}>
            <View
              style={{
                width: 24,
                height: 24,
                borderRadius: 12,
              }}
            />
            <View
              style={{
                width: 24,
                height: 24,
                borderRadius: 12,
              }}
            />
          </View>
        </View>

        {/* Title */}
        <View
          style={{
            width: '80%',
            height: 20,
            borderRadius: 4,
          }}
        />

        {/* Main image */}
        <View
          style={{
            width: '100%',
            height: 200,
            borderRadius: 8,
          }}
        />

        {/* Description */}
        <View style={{gap: 6}}>
          <View
            style={{
              width: '100%',
              height: 16,
              borderRadius: 4,
            }}
          />
          <View
            style={{
              width: '90%',
              height: 16,
              borderRadius: 4,
            }}
          />
        </View>

        {/* Action bar */}
        <View
          style={{
            flexDirection: 'row',
            gap: 16,
            marginTop: 8,
          }}>
          {[1, 2, 3].map((_, index) => (
            <View
              key={index}
              style={{
                width: 80,
                height: 24,
                borderRadius: 12,
              }}
            />
          ))}
        </View>
      </View>
    </SkeletonPlaceholder>
  );
}

const stylesDefault = StyleSheet.create({
  mainContainer: {
    gap: 16,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  mainContent: {
    flex: 1,
  },
  img: {
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
  },
  inforTitle: {
    fontSize: 12,
    color: '#61616B',
  },
  titleStyle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#18181B',
  },
  subTitleStyle: {
    fontSize: 14,
    color: '#61616B',
  },
  bodyContentStyle: {
    fontSize: 14,
    fontWeight: '400',
    color: '#313135',
  },
});

// Định nghĩa các styles cố định để tránh tạo mới mỗi lần render
const imageStyles = StyleSheet.create({
  fullImage: {
    width: '100%',
    height: 234,
    borderRadius: 8,
  },
  halfImage: {
    width: '49.5%',
    height: 234,
    borderRadius: 8,
  },
  topFullImage: {
    width: '100%',
    height: 160,
    borderRadius: 8,
    marginBottom: 2,
  },
  halfSmallImage: {
    width: '49.5%',
    height: 120,
    borderRadius: 8,
  },
  quadImage: {
    width: '49.5%',
    height: 116,
    borderRadius: 8,
    marginBottom: 2,
  },
  quadImageBottom: {
    width: '49.5%',
    height: 116,
    borderRadius: 8,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlayText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
});

const contentStyles = StyleSheet.create({
  readMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 4,
  },
  readMoreText: {
    ...TypoSkin.buttonText5,
    color: ColorThemes.light.Info_Color_Main,
    fontWeight: 'bold',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    flexWrap: 'wrap',
  },
});
