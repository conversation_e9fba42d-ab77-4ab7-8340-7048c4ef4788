import {useDispatch, useSelector} from 'react-redux';
import {AppDispatch, RootState} from '../../../redux/store/store';
import {useEffect} from 'react';
import {myFeedActions} from '../reducers/MyFeedReducer';

export function useMyFeedData(page: number, size: number, cusId: string) {
  const dispatch: AppDispatch = useDispatch();
  const data = useSelector((state: RootState) => state.newsFeed.data);
  const loading = useSelector((state: RootState) => state.newsFeed.loading);
  const error = useSelector((state: RootState) => state.newsFeed.error);
  const reduxPage = useSelector((state: RootState) => state.newsFeed.page);

  useEffect(() => {
    // Chỉ gọi API khi component mount lần đầu
    dispatch(myFeedActions.getNewFeed(page, size, cusId));
  }, [dispatch, page, size, cusId]); // Update dependencies to include page and size

  return {data, loading, error, page: reduxPage};
}
