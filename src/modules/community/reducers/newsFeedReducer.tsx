import {createSlice, Dispatch, PayloadAction} from '@reduxjs/toolkit';
import {DataController} from '../../../base/baseController';
import {randomGID, Ultis} from '../../../utils/Utils';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {StorageContanst} from '../../../Config/Contanst';
import ConfigAPI from '../../../Config/ConfigAPI';
import store from '../../../redux/store/store';
import {ComponentStatus, showSnackbar} from 'wini-mobile-components';
export const FETCH_NEWS_REQUEST = 'FETCH_NEWS_REQUEST';
export const FETCH_NEWS_REQUEST_LOADMORE = 'FETCH_NEWS_REQUEST_LOADMORE';
export const UPDATE_LIKE = 'UPDATE_LIKE';
export const UPDATE_LIKE_COMMENT = 'UPDATE_LIKE_COMMENT';
export const ADD_COMMENT = 'ADD_COMMENT';
export const ADD_BOOKMARK = 'ADD_BOOKMARK';
export const UPDATE_COMMENT_COUNT = 'UPDATE_COMMENT_COUNT';
export const ADD_POST = 'ADD_POST';
export const HIDE_POST = 'HIDE_POST';
export const UPDATE_POST = 'UPDATE_POST';
export const FETCH_ERROR = 'FETCH_ERROR';
const initialState: {
  data: any[];
  loading: boolean;
  loadmore: boolean;
  page: number;
  success: boolean;
  error: any; // Hoặc để null|string nếu muốn cụ thể hơn
} = {
  data: [],
  page: 1,
  loading: true,
  loadmore: false,
  success: false,
  error: null,
};
export const newsFeeedSlice = createSlice({
  name: 'newsFeed',
  initialState,
  reducers: {
    handleActions: (state, action: PayloadAction<any>) => {
      switch (action.payload.type) {
        case ADD_POST: {
          // Ensure new post is added at the beginning of the array
          state.data = [action.payload.post, ...state.data];
          break;
        }
        case UPDATE_POST: {
          // Cập nhật bài đăng trong state
          state.data = state.data.map(post =>
            post.Id === action.payload.post.Id
              ? {
                  ...post,
                  Content: action.payload.post.Content,
                  Img: action.payload.post.Img,
                  DateModified: action.payload.post.DateModified,
                }
              : post,
          );
          break;
        }
        case FETCH_NEWS_REQUEST:
          state.data = action.payload.data;
          state.page = action.payload.page;
          break;
        case FETCH_NEWS_REQUEST_LOADMORE:
          // Tạo một Set các ID hiện có để kiểm tra trùng lặp
          const existingIds = new Set(state.data.map((item: any) => item.Id));
          // Chỉ thêm các mục không trùng lặp
          const newItems = action.payload.data.filter(
            (item: any) => !existingIds.has(item.Id),
          );
          state.data = [...state.data, ...newItems];
          state.page = action.payload.page;
          break;
        case UPDATE_LIKE:
          state.data = state.data.map((news: any) => {
            if (news.Id === action.payload.Id) {
              const currentLikes = news.Likes ?? 0;
              return {
                ...news,
                Likes: action.payload.IsLike
                  ? currentLikes + 1
                  : Math.max(0, currentLikes - 1), // Prevent negative likes
                IsLike: action.payload.IsLike,
              };
            }
            return news;
          });
          break;
        case ADD_BOOKMARK:
          state.data = state.data.map((news: any) => {
            if (news.Id === action.payload.Id) {
              return {
                ...news,
                IsBookmark: action.payload.IsBookmark,
              };
            }
            return news;
          });
          break;
        case UPDATE_COMMENT_COUNT: {
          const {postId, increment} = action.payload;
          return {
            ...state,
            data: state.data.map(post =>
              post.Id === postId
                ? {...post, Comment: (post.Comment || 0) + increment}
                : post,
            ),
          };
        }
        case HIDE_POST: {
          // Xử lý ẩn bài đăng: cập nhật IsHidden và lọc khỏi danh sách hiển thị
          const postId = action.payload.postId;
          state.data = state.data.filter(post => post.Id !== postId);
          break;
        }
        case FETCH_ERROR: {
          // Handle error in a serializable way
          state.error = action.payload.error
            ? 'Error fetching news feed'
            : null;
          state.loading = false;
          break;
        }
        // Remove comment-related cases
      }
      state.loading = false;
      state.loadmore = false;
    },
    onFetching: state => {
      state.data = [];
      state.loading = true;
    },
    onLoadmore: state => {
      state.loadmore = true;
    },
  },
});
export default newsFeeedSlice.reducer;
const {handleActions, onFetching} = newsFeeedSlice.actions;
export class newsFeedActions {
  static getMyFeed =
    (page: number, size: number, cusId: string | null, search?: string) =>
    async (dispatch: Dispatch) => {
      try {
        dispatch(onFetching());
        // Get my posts

        const myPosts = await getPostsForUser(cusId, page, size, search);
        if (!myPosts || myPosts.code !== 200) {
          return;
        }
        // Get all required data in parallel
        const enrichedPosts = await enrichPostsWithData(myPosts.data, cusId);
        dispatch(
          handleActions({
            type: page > 1 ? FETCH_NEWS_REQUEST_LOADMORE : FETCH_NEWS_REQUEST,
            data: enrichedPosts,
            page: page,
          }),
        );
      } catch (error) {
        console.error('Error fetching news feed:', error);
        dispatch(handleActions({type: FETCH_ERROR, error: true}));
      }
    };

  // tạo action lấy bài viết đã lưu
  static getNewFeedSaved =
    (page: number, size: number) => async (dispatch: Dispatch) => {
      try {
        dispatch(onFetching());
        const cusId = store.getState().customer.data?.Id;
        const postController = new DataController('Post_Bookmark');
        const postResult = await postController.getListSimple({
          query: `@CustomerId: {*${cusId}*}`,
          page,
          size,
        });
        const postIds = postResult.data.map((post: any) => post.PostId);
        const Postcontroller = new DataController('Post');
        const postDetailResult = await Postcontroller.getListSimple({
          query: `@Id:{${postIds.join(' | ')}}`,
          page,
          size,
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });
        const enrichedPosts = await enrichPostsWithData(
          postDetailResult.data,
          cusId,
        );
        dispatch(
          handleActions({
            type: page > 1 ? FETCH_NEWS_REQUEST_LOADMORE : FETCH_NEWS_REQUEST,
            data: enrichedPosts,
            page: page,
          }),
        );
      } catch (error) {
        console.error('Error fetching news feed:', error);
        dispatch(handleActions({type: FETCH_ERROR, error: true}));
      }
    };

  //tạo action lấy bài viết phổ biến có nhiều lượt tương tác như like, share, bình luận
  static getNewFeedPopular =
    (page: number, size: number) => async (dispatch: Dispatch) => {
      try {
        dispatch(onFetching());
        const cusId = store.getState().customer.data?.Id;
        //lấy bài viết gần nhất trong 7 ngày
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        const startTimestamp = sevenDaysAgo.getTime();
        const endTimestamp = Date.now();
        const Postcontroller = new DataController('Post');

        const postResult = await Postcontroller.getListSimple({
          query: `@DateCreated:[${startTimestamp} ${endTimestamp}] -@IsHidden:{true}`,
          page,
          size,
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });

        // Kiểm tra xem postResult.data có tồn tại và có phải là mảng không
        if (
          !postResult.data ||
          !Array.isArray(postResult.data) ||
          postResult.data.length === 0
        ) {
          dispatch(
            handleActions({
              type: FETCH_NEWS_REQUEST,
              data: [],
              page: page,
            }),
          );
          return;
        }

        const enrichedPosts = await enrichPostsWithDataPopular(
          postResult.data,
          cusId,
        );

        dispatch(
          handleActions({
            type: page > 1 ? FETCH_NEWS_REQUEST_LOADMORE : FETCH_NEWS_REQUEST,
            data: enrichedPosts,
            page: page,
          }),
        );
      } catch (error) {
        console.error('Error fetching news feed:', error);
        dispatch(handleActions({type: FETCH_ERROR, error: true}));
      }
    };

  static addBookmark =
    (id: string, isBookmark: boolean) => async (dispatch: Dispatch) => {
      var cusId = store.getState().customer.data?.Id;
      const controller = new DataController('Post_Bookmark');

      if (!cusId) {
        return;
      }
      if (isBookmark === true) {
        const result = await controller.getListSimple({
          query: `@CustomerId: {${cusId}} @PostId:{${id}}`,
        });
        if (result.data?.length > 0) {
          const unbookmark = await controller.delete([result.data[0].Id]);
          if (unbookmark.code === 200) {
            dispatch(
              handleActions({
                type: ADD_BOOKMARK,
                Id: id,
                IsBookmark: false,
              }),
            );
          }
        }
      } else {
        const data = {
          Id: randomGID(),
          CustomerId: cusId,
          PostId: id,
          DateCreated: new Date().getTime(),
        };
        const result = await controller.add([data]);
        if (result.code === 200) {
          dispatch(
            handleActions({
              type: ADD_BOOKMARK,
              Id: id,
              IsBookmark: true,
            }),
          );
        }
      }
    };

  static updateLike =
    (id: string, isUnLike: boolean, forNew?: boolean) =>
    async (dispatch: Dispatch) => {
      const likeController = new DataController('Like');
      const cusId = store.getState().customer.data?.Id;

      if (!cusId) {
        console.error('User not authenticated');
        return;
      }

      try {
        if (isUnLike === true) {
          // Unlike - Remove existing like
          const result = await likeController.getListSimple({
            query: forNew
              ? `@CustomerId: {${cusId}} @NewsId:{${id}}`
              : `@CustomerId: {${cusId}} @PostId:{${id}}`,
          });

          if (result.data?.length > 0) {
            const unlike = await likeController.delete([result.data[0].Id]);
            if (unlike.code === 200) {
              dispatch(
                handleActions({
                  type: UPDATE_LIKE,
                  Id: id,
                  IsLike: false,
                }),
              );
            } else {
              console.error('Failed to unlike post:', unlike);
            }
          }
        } else {
          // Like - Add new like
          const likeData: any = {
            Id: randomGID(),
            CustomerId: cusId,
            Type: 1,
            DateCreated: new Date().getTime(),
            Name: `${
              store.getState().customer.data?.Name
            } thích bài viết của bạn`,
          };

          // Set the correct field based on forNew parameter
          if (forNew) {
            likeData.NewsId = id;
          } else {
            likeData.PostId = id;
          }

          const result = await likeController.add([likeData]);
          if (result.code === 200) {
            dispatch(
              handleActions({
                type: UPDATE_LIKE,
                Id: id,
                IsLike: true,
              }),
            );
          } else {
            console.error('Failed to like post:', result);
          }
        }
      } catch (error) {
        console.error('Error updating like:', error);
      }
    };

  static setLike =
    (id: string, isUnLike: boolean) => async (dispatch: Dispatch) => {
      dispatch(
        handleActions({
          type: UPDATE_LIKE,
          Id: id,
          IsLike: isUnLike,
        }),
      );
    };

  static updateCommentCount =
    (id: string, increment: number) => async (dispatch: Dispatch) => {
      dispatch(
        handleActions({
          type: UPDATE_COMMENT_COUNT,
          postId: id,
          increment: increment,
        }),
      );
    };

  static addPost = (postData: any) => async (dispatch: Dispatch) => {
    try {
      const customer = store.getState().customer.data;

      if (!customer) {
        return null;
      }
      const postController = new DataController('Post');
      const response = await postController.add([postData]);
      if (response?.code === 200) {
        // Get user info for the post
        if (customer) {
          const postWithUser = {
            ...postData,
            Likes: 0,
            IsLike: false,
            Comment: 0,
            IsBookmark: false,
            relativeUser: {
              image: customer.AvatarUrl,
              title: customer.Name,
              subtitle: Ultis.getDiffrentTime(postData.DateCreated),
            },
          };
          // Dispatch action to add post to redux store
          dispatch(
            handleActions({
              type: ADD_POST,
              post: postWithUser,
            }),
          );

          return postWithUser;
        }
      }
      return null;
    } catch (error) {
      console.error('Error adding post:', error);
      return null;
    }
  };

  static hidePost = (post: any) => async (dispatch: Dispatch) => {
    try {
      const postController = new DataController('Post');
      var cusId = store.getState().customer.data?.Id;

      if (!cusId) {
        console.error('User not authenticated');
        return false;
      }
      // Kiểm tra quyền: chỉ người tạo bài đăng mới có thể ẩn
      if (post.CustomerId !== cusId) {
        // console.error('Not authorized to hide this post');
        showSnackbar({
          message: 'Bạn không có quyền ẩn bài đăng này',
          status: ComponentStatus.ERROR,
        });
        return false;
      }

      // Cập nhật trạng thái IsHidden của bài đăng
      const updateResult = await postController.edit([
        {
          ...post,
          IsHidden: true,
        },
      ]);
      if (updateResult.code === 200) {
        // Cập nhật state trong Redux
        dispatch(
          handleActions({
            type: HIDE_POST,
            postId: post.Id,
          }),
        );
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error hiding post:', error);
      return false;
    }
  };

  //tạo action xóa post
  static deletePost = (post: any) => async (dispatch: Dispatch) => {
    try {
      const postController = new DataController('Post');
      var cusId = store.getState().customer.data?.Id;
      if (!cusId) {
        console.error('User not authenticated');
        return false;
      }
      // Kiểm tra quyền: chỉ người tạo bài đăng mới có thể xóa
      // const post = await postController.getById(postId);
      if (!post || post.CustomerId !== cusId) {
        // console.error('Not authorized to delete this post');
        showSnackbar({
          message: 'Bạn không có quyền xóa bài đăng này',
          status: ComponentStatus.ERROR,
        });
        return false;
      }
      const deleteResult = await postController.delete([post.Id]);
      if (deleteResult.code === 200) {
        dispatch(
          handleActions({
            type: HIDE_POST,
            postId: post.Id,
          }),
        );
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting post:', error);
      return false;
    }
  };

  static hidePostNocall = (PostId: string) => async (dispatch: Dispatch) => {
    dispatch(
      handleActions({
        type: HIDE_POST,
        postId: PostId,
      }),
    );
  };
  static updatePost = (post: any) => async (dispatch: Dispatch) => {
    try {
      // Dispatch action để cập nhật post trong Redux store
      dispatch(
        handleActions({
          type: UPDATE_POST,
          post: post,
        }),
      );

      return post;
    } catch (error) {
      console.error('Error updating post in Redux store:', error);
      return null;
    }
  };
}

// Helper functions
export async function getPostsForUser(
  cusId: string | null,
  page: number,
  size: number,
  search?: string,
) {
  var querySearch = `@Name: (*${search}*) `;
  const Postcontroller = new DataController('Post');
  if (!cusId) {
    return Postcontroller.getListSimple({
      page,
      size,
      query: search
        ? querySearch + `@CustomerId:{${ConfigAPI.adminITM}} -@IsHidden:{true}`
        : `@CustomerId:{${ConfigAPI.adminITM}} -@IsHidden:{true}`,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
  }

  // const CustomerController = new DataController('Customer');
  // const cus = await CustomerController.getById(cusId);

  // if (cus?.code !== 200) {
  //   return null;
  // }
  var lstId = [cusId];
  const query = search
    ? querySearch + `@CustomerId:{${lstId.join(' | ')}} -@IsHidden:{true}`
    : `@CustomerId:{${lstId.join(' | ')}} -@IsHidden:{true}`;

  return Postcontroller.getListSimple({
    page,
    size,
    query,
    sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
  });
}

async function enrichPostsWithData(posts: any[], cusId: string | null) {
  // Get all unique customer IDs and post IDs
  const customerIds = [...new Set(posts.map(post => post.CustomerId))];

  const postIds = posts.map(post => post.Id);
  // Fetch all data in parallel
  const [customers, likes, comments, bookmarks] = await Promise.all([
    fetchCustomers(customerIds),
    fetchLikes(postIds),
    fetchComments(postIds),
    fetchBookmarks(postIds),
  ]);
  // Enrich posts with fetched data
  return posts.map(post => {
    const customer = customers.find((c: any) => c.Id === post.CustomerId);
    const postLikes = likes.filter((l: any) => l.PostId === post.Id);
    const commentCount =
      comments.find((c: any) => c.PostId === post.Id)?.CommentsCount || 0;
    const isBookmarked = bookmarks.some((b: any) => b.PostId === post.Id);

    return {
      ...post,
      Likes: postLikes.length,
      IsLike: cusId
        ? postLikes.some((like: any) => like.CustomerId === cusId)
        : false,
      IsBookmark: isBookmarked,
      Comment: commentCount,
      relativeUser: customer
        ? {
            image: customer.AvatarUrl,
            title: customer.Name,
            subtitle: Ultis.getDiffrentTime(post.DateCreated),
          }
        : null,
    };
  });
}

async function enrichPostsWithDataPopular(posts: any[], cusId: string | null) {
  // Get all unique customer IDs and post IDs
  const customerIds = [...new Set(posts.map(post => post.CustomerId))];
  const postIds = posts.map(post => post.Id);

  // Fetch all data in parallel
  const [likes, customers, comments, bookmarks] = await Promise.all([
    fetchLikes(postIds),
    fetchCustomers(customerIds),
    fetchComments(postIds),
    fetchBookmarks(postIds),
  ]);

  // Enrich posts with fetched data
  return posts.map(post => {
    const customer = customers.find((c: any) => c.Id === post.CustomerId);
    const commentCount =
      comments.find((c: any) => c.PostId === post.Id)?.CommentsCount || 0;
    const postLikes = likes.filter((l: any) => l.PostId === post.Id);
    const isBookmarked = bookmarks.some((b: any) => b.PostId === post.Id);
    return {
      ...post,
      Likes: postLikes.length,
      IsLike: cusId
        ? postLikes.some((like: any) => like.CustomerId === cusId)
        : false,
      IsBookmark: isBookmarked,
      Comment: commentCount,
      relativeUser: customer
        ? {
            image: customer.AvatarUrl,
            title: customer.Name,
            subtitle: Ultis.getDiffrentTime(post.DateCreated),
          }
        : null,
    };
  });
}

export async function fetchCustomers(customerIds: string[]) {
  const customerController = new DataController('Customer');
  const response = await customerController.getListSimple({
    query: `@Id:{${customerIds.join(' | ')}}`,
  });
  return response.code === 200 ? response.data : [];
}

export async function fetchLikes(postIds: string[], forNew?: boolean) {
  const likeController = new DataController('Like');
  const response = await likeController.getListSimple({
    query: forNew
      ? `@NewsId:{${postIds.join(' | ')}}`
      : `@PostId:{${postIds.join(' | ')}} `,
  });
  return response.code === 200 ? response.data : [];
}
async function fetchGroups(groupIds: string[]) {
  const groupController = new DataController('Group');
  const response = await groupController.getListSimple({
    query: `@Id:{${groupIds.join(' | ')}}`,
  });
  return response.code === 200 ? response.data : [];
}
export async function fetchComments(postIds: string[], forNew?: boolean) {
  const commentController = new DataController('Comments');
  const response = await commentController.group({
    searchRaw: forNew
      ? `@NewsId:{${postIds.join(' | ')}}`
      : `@PostId:{${postIds.join(' | ')}}`,
    reducers: forNew
      ? 'LOAD * GROUPBY 1 @NewsId REDUCE COUNT 0 AS CommentsCount'
      : 'LOAD * GROUPBY 1 @PostId REDUCE COUNT 0 AS CommentsCount',
  });

  // Thêm kiểm tra trước khi trả về data
  return response.code === 200 && response.data ? response.data : [];
}

async function fetchBookmarks(postIds: string[]) {
  const bookmarkController = new DataController('Post_Bookmark');
  var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
  const response = await bookmarkController.getListSimple({
    query: `@PostId:{${postIds.join(' | ')}} @CustomerId: {${cusId}}`,
  });
  return response.code === 200 ? response.data : [];
}
