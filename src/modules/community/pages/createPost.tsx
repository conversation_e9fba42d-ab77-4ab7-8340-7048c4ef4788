/* eslint-disable react-native/no-inline-styles */
import React, {useRef, useState, useEffect} from 'react';
import {SafeAreaView, View, Alert, Text} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import ScreenHeader from '../../../Screen/Layout/header';
import {AppButton, FBottomSheet, Winicon} from 'wini-mobile-components';
import {navigateBack} from '../../../router/router';
import RichTextComposer, {
  RichTextComposerRef,
  PostData,
} from '../components/PostEditor';
import {useDispatch} from 'react-redux';
import {DataController} from '../../../base/baseController';
import store, {AppDispatch} from '../../../redux/store/store';
import {TypoSkin} from '../../../assets/skin/typography';
import {newsFeedActions} from '../reducers/newsFeedReducer';
import {useRoute} from '@react-navigation/native';
import {BaseDA} from '../../../base/BaseDA';
import {randomGID} from '../../../utils/Utils';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import ConfigAPI from '../../../Config/ConfigAPI';
import {myFeedActions} from '../reducers/MyFeedReducer';
import {InforHeader} from '../../../Screen/Layout/headers/inforHeader';

const CreatePost = () => {
  const bottomSheetRef = useRef<any>(null);
  const richTextRef = useRef<RichTextComposerRef>(null);
  const route = useRoute<any>();
  const {groupId, editPost} = route.params || {}; // Lấy thông tin post cần edit nếu có
  const customer = useSelectorCustomerState().data;
  const isEditMode = !!editPost; // Kiểm tra xem có phải mode edit không

  const [isSubmitting, setIsSubmitting] = useState(false);
  const dispatch: AppDispatch = useDispatch();
  const [postData, setPostData] = useState<PostData>({
    text: '',
    segments: [],
    images: [],
    html: undefined,
  });

  // Xử lý dữ liệu ban đầu nếu đang ở chế độ edit
  useEffect(() => {
    if (isEditMode && richTextRef.current && editPost) {
      // Xử lý danh sách ID ảnh
      const imageIds: string[] = [];
      if (editPost.Img) {
        // Nếu Img là chuỗi chứa nhiều ID ngăn cách bởi dấu phẩy
        if (typeof editPost.Img === 'string' && editPost.Img.includes(',')) {
          imageIds.push(
            ...editPost.Img.split(',').filter(
              (img: string) => img.trim() !== '',
            ),
          );
        }
        // Nếu Img là một ID duy nhất
        else if (
          typeof editPost.Img === 'string' &&
          editPost.Img.trim() !== ''
        ) {
          imageIds.push(editPost.Img.trim());
        }
        // Nếu Img là mảng
        else if (Array.isArray(editPost.Img)) {
          imageIds.push(
            ...editPost.Img.filter((img: string) => img && img.trim() !== ''),
          );
        }
      }

      // Thiết lập nội dung cho RichTextComposer
      richTextRef.current.setContent('', [], editPost.Content, imageIds);
    }
  }, [isEditMode, editPost]);

  // Callback khi dữ liệu từ RichTextComposer thay đổi
  const handlePostDataChange = (data: PostData) => {
    setPostData(data);
  };

  // Hàm xử lý khi submit form
  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      // Lấy dữ liệu từ RichTextComposer
      const editorData = richTextRef.current?.getPostData() || postData;

      // Tạo dữ liệu bài đăng
      const newPostData: any = {
        Content: editorData.html ?? editorData.text,
        Images: editorData.images,
        lstImg: [],
      };

      // Xử lý upload ảnh mới (nếu có)
      const newImages = editorData.images.filter(img => !img.existingId);
      if (newImages.length > 0) {
        const fileToUpload = newImages.map((img: any) => {
          return {
            uri: img.path,
            type: img.mime,
            name: img.filename ?? 'new file img',
          };
        });

        const uploadResult = await BaseDA.uploadFiles(fileToUpload);
        if (uploadResult?.length > 0) {
          newPostData.lstImg = uploadResult.map((item: any) => item.Id);
        }
      }

      // Lấy danh sách ID ảnh đã tồn tại
      const existingImageIds = editorData.images
        .filter(img => img.existingId)
        .map(img => img.existingId as string);

      // Kết hợp ID ảnh đã tồn tại và ID ảnh mới upload
      const allImageIds = [...existingImageIds, ...newPostData.lstImg];
      if (isEditMode) {
        // Nếu đang edit, cập nhật bài đăng hiện có
        const updatedPost = {
          Id: editPost.Id,
          CustomerId: editPost.CustomerId,
          GroupId: editPost.GroupId,
          IsHidden: editPost.IsHidden,
          DateCreated: editPost.DateCreated,
          ListTag: editPost.ListTag,
          IsFavorite: editPost.IsFavorite,
          IsPinned: editPost.IsPinned,
          Status: editPost.Status,
          Name: editPost.Name,
          Title: editPost.Title,
          Content: newPostData.Content,
          Img: allImageIds.join(','),
          DateModified: new Date().getTime(),
        };

        // Gọi API cập nhật bài đăng
        const postController = new DataController('Post');
        const result = await postController.edit([updatedPost]);
        if (result.code === 200) {
          // Cập nhật state trong Redux
          const newfeed = store
            .getState()
            .newsFeed.data.find((item: any) => item.Id === updatedPost.Id);
          if (newfeed) {
            dispatch(newsFeedActions.updatePost(updatedPost));
          }
          setIsSubmitting(false);
          richTextRef.current?.clearContent();
          navigateBack();
        } else {
          throw new Error('Cập nhật bài đăng thất bại');
        }
      } else {
        // Nếu đang tạo mới, thêm bài đăng mới
        const newPost = {
          Content: newPostData.Content,
          Img: allImageIds.join(','),
          Id: randomGID(),
          CustomerId: customer.Id,
          GroupId: groupId,
          DateCreated: new Date().getTime(),
          IsHidden: false,
        };
        const postWithUser = {
          ...newPost,
          Likes: 0,
          IsLike: false,
          GroupId: groupId,
          Comment: 0,
          IsBookmark: false,
          relativeUser: {
            image: customer.AvatarUrl,
            title: customer.Name,
            subtitle: 'Just now',
          },
        };
        if (!groupId) {
          dispatch(newsFeedActions.addPost(newPost));
          dispatch(myFeedActions.addPostNoCall(postWithUser));
        } else {
          dispatch(myFeedActions.addPostNoCall(postWithUser));
          dispatch(newsFeedActions.addPostNoCall(postWithUser));
        }
        setIsSubmitting(false);
        richTextRef.current?.clearContent();
        navigateBack();
      }
    } catch (error) {
      console.error('Submit error:', error);
      setIsSubmitting(false);
      Alert.alert('Lỗi', 'Đã xảy ra lỗi khi xử lý bài đăng. Vui lòng thử lại.');
    }
  };

  return (
    <View
      style={{
        flex: 1,
        height: '100%',
        width: '100%',
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FBottomSheet ref={bottomSheetRef} />
      <InforHeader
        title={isEditMode ? 'Chỉnh sửa bài đăng' : 'Tạo bài đăng'}
        showAction
        customActions={
          <AppButton
            title={isEditMode ? 'Cập nhật' : 'Đăng'}
            backgroundColor={ColorThemes.light.transparent}
            textColor={ColorThemes.light.primary_main_color}
            borderColor="transparent"
            containerStyle={{padding: 4, marginRight: 16}}
            onPress={
              isSubmitting || (!postData.text && postData.images.length === 0)
                ? undefined
                : handleSubmit
            }
            textStyle={{
              ...TypoSkin.buttonText3,
              color:
                isSubmitting || (!postData.text && postData.images.length === 0)
                  ? ColorThemes.light.Neutral_Text_Color_Subtitle
                  : ColorThemes.light.Primary_Color_Main,
            }}
          />
        }
      />
      {/* Sử dụng RichTextComposer */}
      <RichTextComposer
        ref={richTextRef}
        onDataChange={handlePostDataChange}
        initialText=""
        initialImages={[]}
        maxImages={10}
      />
    </View>
  );
};

export default CreatePost;
