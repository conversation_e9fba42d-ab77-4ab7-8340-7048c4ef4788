import React, {useEffect, useRef, useState, useCallback, useMemo} from 'react';
import {
  Text,
  useWindowDimensions,
  View,
  RefreshControl,
  StyleSheet,
  ScrollView,
  Dimensions,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import {AppSvg, Winicon, Rating, FDialog} from 'wini-mobile-components';
import {useNavigation, useRoute} from '@react-navigation/native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useDispatch} from 'react-redux';
import FastImage from 'react-native-fast-image';
import RenderHTML from 'react-native-render-html';
import {SwiperFlatList} from 'react-native-swiper-flatlist';

// Internal imports
import {ProductDA} from './productDA';
import {CustomerDA} from '../customer/da';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {RootScreen} from '../../router/router';
import {Ultis} from '../../utils/Utils';
import iconSvg from '../../svg/icon';
import {CartActions} from '../../redux/reducers/CartReducer';
import CartIcon from '../../components/CartIcon';
import {AppDispatch} from '../../redux/store/store';
import ConfigAPI from '../../Config/ConfigAPI';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {dialogCheckAcc} from '../../Screen/Layout/mainLayout';
import {updateFavoriteProduct} from '../../redux/actions/productAction';
import {DataController} from '../../base/baseController';
import ClickableImage from '../../components/ClickableImage';
import SquareProductCard from '../../components/SquareProductCard';
import {TypeMenuPorduct} from '../../Config/Contanst';
import SuggestionProductSection from './section/SuggestionProductSection';

const {width} = Dimensions.get('window');

// Constants
const ITEM_WIDTH = width * 0.32;
const ITEM_SPACING = 10;

// TypeScript interfaces
interface ProductData {
  Id: string;
  Name: string;
  Price: number;
  Discount?: number;
  Sold: number;
  InStock: number;
  BrandName?: string;
  CategoryId: string;
  CategoryName?: string;
  Content?: string;
  IsFreeShip?: boolean;
  IsFavorite?: boolean;
  rating?: number;
  ListImg?: string;
  Img?: string;
}

interface ShopData {
  Id: string;
  Name: string;
  CustomerId: string;
  Mobile?: string;
  Img?: string;
  OwnerName?: string;
  rating?: number;
  totalProducts?: number;
  totalOrder?: number;
  products?: ProductData[];
}

// Skeleton Components
const SkeletonBox: React.FC<{
  width: number | string;
  height: number;
  style?: any;
}> = ({width, height, style}) => (
  <View
    style={[
      {
        width,
        height,
        backgroundColor: '#E0E0E0',
        borderRadius: 8,
      },
      style,
    ]}
  />
);

const ProductDetailSkeleton: React.FC = () => (
  <View style={styles.container}>
    {/* Header Skeleton */}
    <View style={styles.header}>
      <SkeletonBox width={40} height={40} style={{borderRadius: 20}} />
      <View style={styles.headerRight}>
        <SkeletonBox width={40} height={40} style={{borderRadius: 20}} />
        <SkeletonBox width={40} height={40} style={{borderRadius: 20}} />
      </View>
    </View>

    <ScrollView style={styles.scrollView}>
      {/* Image Skeleton */}
      <SkeletonBox width="100%" height={350} style={{borderRadius: 0}} />

      {/* Price Skeleton */}
      <View style={styles.priceContainer}>
        <SkeletonBox width={120} height={24} />
        <View style={styles.actionButtons}>
          <SkeletonBox width={24} height={24} />
          <SkeletonBox width={24} height={24} />
        </View>
      </View>

      {/* Title Skeleton */}
      <View style={{paddingHorizontal: 16, paddingVertical: 8}}>
        <SkeletonBox width="100%" height={20} />
        <SkeletonBox width="70%" height={20} style={{marginTop: 8}} />
      </View>

      {/* Details Skeleton */}
      <View style={styles.detailsContainer}>
        <View style={styles.detailsHorizontalContainer}>
          <SkeletonBox width={60} height={16} />
          <SkeletonBox width={60} height={16} />
          <SkeletonBox width={60} height={16} />
        </View>
      </View>

      {/* Shipping Skeleton */}
      <View style={styles.shippingContainer}>
        <SkeletonBox width={20} height={20} />
        <SkeletonBox width={100} height={16} style={{marginLeft: 8}} />
      </View>

      {/* Rating Skeleton */}
      <View style={styles.ratingContainer}>
        <SkeletonBox width={100} height={20} />
        <SkeletonBox width={60} height={16} style={{marginLeft: 'auto'}} />
      </View>

      {/* Seller Skeleton */}
      <View style={styles.sellerContainer}>
        <SkeletonBox width={40} height={40} style={{borderRadius: 20}} />
        <View style={{marginLeft: 12, flex: 1}}>
          <SkeletonBox width={120} height={16} />
          <SkeletonBox width={80} height={12} style={{marginTop: 4}} />
        </View>
      </View>

      {/* Shop Info Skeleton */}
      <View
        style={{
          paddingHorizontal: 24,
          paddingVertical: 12,
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}>
        <View style={{alignItems: 'center'}}>
          <SkeletonBox width={30} height={16} />
          <SkeletonBox width={50} height={12} style={{marginTop: 4}} />
        </View>
        <View style={{alignItems: 'center'}}>
          <SkeletonBox width={30} height={16} />
          <SkeletonBox width={60} height={12} style={{marginTop: 4}} />
        </View>
        <View style={{alignItems: 'center'}}>
          <SkeletonBox width={30} height={16} />
          <SkeletonBox width={40} height={12} style={{marginTop: 4}} />
        </View>
      </View>
    </ScrollView>

    {/* Bottom Actions Skeleton */}
    <View style={styles.bottomActions}>
      <SkeletonBox width="33%" height={64} style={{borderRadius: 0}} />
      <SkeletonBox width="33%" height={64} style={{borderRadius: 0}} />
      <SkeletonBox width="34%" height={64} style={{borderRadius: 0}} />
    </View>
  </View>
);

export default function ProductDetail() {
  // State management
  const [like, setLike] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [data, setData] = useState<ProductData | null>(null);
  const [shop, setShop] = useState<ShopData | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState<number>(0);
  const [productImages, setProductImages] = useState<string[]>([]);

  // Hooks
  const route = useRoute<any>();
  const {id} = route.params;
  const navigation = useNavigation<any>();
  const {width: windowWidth} = useWindowDimensions();
  const dispatch: AppDispatch = useDispatch();

  // Refs
  const scrollviewRef = useRef<ScrollView>(null);
  const DialogRef = useRef<any>(null);
  const swiperRef = useRef<SwiperFlatList>(null);

  // Redux state
  const customerAddress = useSelectorCustomerState().myAddress;
  const customer = useSelectorCustomerState().data;

  // Data controllers - memoized to prevent recreation
  const customerDA = useMemo(() => new CustomerDA(), []);
  const productDA = useMemo(() => new ProductDA(), []);
  const productFavorite = useMemo(
    () => new DataController('ProductFavorite'),
    [],
  );
  const ratingController = useMemo(() => new DataController('Rating'), []);
  const productController = useMemo(() => new DataController('Product'), []);
  const orderController = useMemo(() => new DataController('Order'), []);

  const getData = useCallback(async (): Promise<void> => {
    try {
      if (!id) {
        setLoading(false);
        setRefreshing(false);
        return;
      }

      const result = await productDA.getProductDetail(id);
      if (!result) {
        setLoading(false);
        setRefreshing(false);
        return;
      }

      const product = result.data[0];
      const shopData = result.Shop[0];

      // Get shop owner information
      const customerShop = await customerDA.getCustomerItem(
        shopData.CustomerId,
      );

      // Get shop rating
      const ratingShop = await ratingController.group({
        reducers:
          'LOAD * GROUPBY 1 @ShopId REDUCE SUM 1 @Value AS TotalRate REDUCE COUNT 0 AS CountRate',
        searchRaw: `@ShopId: {${shopData.Id}}`,
      });

      if (ratingShop.code === 200) {
        const totalRate = parseFloat(ratingShop.data[0]?.TotalRate || '0');
        const countRate = parseFloat(ratingShop.data[0]?.CountRate || '0');
        shopData.rating = countRate > 0 ? totalRate / countRate : 0;
      }

      // Get total products of shop
      const totalProducts = await productController.group({
        reducers: 'LOAD * GROUPBY 1 @ShopId REDUCE COUNT 0 AS CountProduct',
        searchRaw: `@ShopId: {${shopData.Id}}`,
      });

      if (totalProducts.code === 200) {
        shopData.totalProducts = totalProducts.data[0]?.CountProduct || 0;
      }

      // Get total orders of shop
      const totalOrder = await orderController.group({
        reducers: 'LOAD * GROUPBY 1 @ShopId REDUCE COUNT 0 AS CountOrder',
        searchRaw: `@ShopId: {${shopData.Id}}`,
      });

      if (totalOrder.code === 200) {
        shopData.totalOrder = totalOrder.data[0]?.CountOrder || 0;
      }

      // Get related products from same shop and category
      const listProduct = await productController.getListSimple({
        page: 1,
        size: 10,
        query: `@ShopId: {${shopData.Id}} @Status: [${TypeMenuPorduct.InStock.id}] @CategoryId: {${product.CategoryId}}`,
      });

      if (listProduct.code === 200) {
        shopData.products = listProduct.data?.filter(
          (item: any) => item?.Id !== id,
        );
      }

      // Prepare shop information
      const shopInfo: ShopData = {
        ...shopData,
        Img: customerShop?.AvatarUrl
          ? `${ConfigAPI.urlImg}${customerShop.AvatarUrl}`
          : '',
        OwnerName: customerShop?.Name,
      };

      setShop(shopInfo);

      // Process product images
      if (product?.ListImg || product?.Img) {
        let listImg = product?.ListImg
          ? product.ListImg.split(',')
          : [product.Img];
        listImg = listImg.filter((item: string) => item !== '');
        listImg = listImg.map((item: string) => `${ConfigAPI.urlImg}${item}`);
        setProductImages(listImg);
      }

      // Add brand and category names
      if (product?.BrandId && result?.Brand?.[0]) {
        product.BrandName = result.Brand[0].Name;
      }
      if (product?.CategoryId && result?.Category?.[0]) {
        product.CategoryName = result.Category[0].Name;
      }

      // Get product rating
      const ratingResult = await ratingController.group({
        reducers:
          'LOAD * GROUPBY 1 @ProductId REDUCE SUM 1 @Value AS TotalRate REDUCE COUNT 0 AS CountRate',
        searchRaw: `@ProductId: {${product.Id}}`,
      });

      if (ratingResult.code === 200) {
        const totalRate = parseFloat(ratingResult.data[0]?.TotalRate || '0');
        const countRate = parseFloat(ratingResult.data[0]?.CountRate || '0');
        product.rating = countRate > 0 ? totalRate / countRate : 0;
      }

      // Check if product is favorite
      if (customer?.Id) {
        const favoriteResult = await productFavorite.getListSimple({
          page: 1,
          size: 1,
          query: `@ProductId:{${product.Id}} @CustomerId:{${customer.Id}}`,
        });

        if (favoriteResult.code === 200) {
          product.IsFavorite = favoriteResult.data.length > 0;
          setLike(favoriteResult.data.length > 0);
        }
      }

      setData(product);
    } catch (error) {
      console.error('Error fetching product data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [
    id,
    productDA,
    customerDA,
    ratingController,
    productController,
    orderController,
    productFavorite,
    customer?.Id,
  ]);

  useEffect(() => {
    getData();
  }, [getData]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    getData();
  }, [getData]);

  // Memoized callbacks for FlatList performance
  const handleProductPress = useCallback(
    (productId: string) => {
      navigation.push(RootScreen.ProductDetail, {id: productId});
    },
    [navigation],
  );

  const renderProductItem = useCallback(
    ({item}: {item: ProductData}) => (
      <SquareProductCard
        item={item}
        onPress={() => handleProductPress(item.Id)}
        width={ITEM_WIDTH}
        showRating={true}
      />
    ),
    [handleProductPress],
  );

  const keyExtractor = useCallback(
    (item: ProductData) => `product-${item.Id}`,
    [],
  );

  // Optimized getItemLayout for better FlatList performance
  const getItemLayout = useCallback(
    (_: any, index: number) => ({
      length: ITEM_WIDTH,
      offset: (ITEM_WIDTH + ITEM_SPACING) * index,
      index,
    }),
    [],
  );

  // Memoized callbacks for SwiperFlatList performance
  const handleImageIndexChange = useCallback(({index}: {index: number}) => {
    setCurrentImageIndex(index);
  }, []);

  const renderImageItem = useCallback(
    ({item}: {item: string}) => (
      <ClickableImage
        key={item}
        source={{uri: item}}
        resizeMode={FastImage.resizeMode.cover}
        style={styles.productImage}
      />
    ),
    [],
  );

  // Show skeleton while loading
  if (loading) {
    return <ProductDetailSkeleton />;
  }

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      {/* Header with back button, cart and menu */}
      <FDialog ref={DialogRef} />
      <View style={styles.header}>
        <TouchableOpacity
          style={{
            paddingRight: 16,
            paddingVertical: 8,
            alignItems: 'center',
            zIndex: 10,
          }}
          activeOpacity={0.7}
          hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
          onPress={() => {
            navigation.goBack();
          }}>
          <View
            style={{
              gap: 4,
              flexDirection: 'row',
              width: '100%',
              alignItems: 'center',
              borderRadius: 20,
              backgroundColor:
                ColorThemes.light.neutral_absolute_background_color,
              padding: 6,
            }}>
            <Winicon
              src="outline/arrows/left-arrow"
              size={20}
              color={ColorThemes.light.neutral_text_title_color}
            />
          </View>
        </TouchableOpacity>
        <View style={styles.headerRight}>
          <TouchableOpacity
            style={styles.cartButton}
            onPress={() => navigation.navigate(RootScreen.CartPage)}>
            <View
              style={{
                gap: 4,
                flexDirection: 'row',
                width: '100%',
                alignItems: 'center',
                borderRadius: 20,
                backgroundColor:
                  ColorThemes.light.neutral_absolute_background_color,
                padding: 4,
              }}>
              <CartIcon color="#1C33FF" size={20} isHome={false} />
            </View>
          </TouchableOpacity>
          <TouchableOpacity>
            <View
              style={{
                gap: 4,
                flexDirection: 'row',
                width: '100%',
                alignItems: 'center',
                borderRadius: 20,
                backgroundColor:
                  ColorThemes.light.neutral_absolute_background_color,
                padding: 8,
              }}>
              <Winicon
                src="fill/layout/dots-vertical"
                size={20}
                color="#1C33FF"
              />
            </View>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        ref={scrollviewRef}
        nestedScrollEnabled
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }
        style={styles.scrollView}
        removeClippedSubviews={true}
        scrollEventThrottle={16}>
        {/* Product Image Carousel */}
        <View style={styles.imageContainer}>
          <SwiperFlatList
            ref={swiperRef}
            autoplay
            autoplayDelay={5}
            autoplayLoop
            showPagination={false}
            data={productImages}
            onChangeIndex={handleImageIndexChange}
            renderItem={renderImageItem}
          />
          <View style={styles.imageCounter}>
            <Text style={styles.imageCounterText}>
              {currentImageIndex + 1}/{productImages.length}
            </Text>
          </View>
        </View>
        {/* Price and Actions */}
        <View style={styles.priceContainer}>
          {data?.Discount && data?.Discount > 0 ? (
            <View style={{flexDirection: 'row', gap: 8, alignItems: 'center'}}>
              <Text style={styles.priceText}>
                {Ultis.money(
                  data?.Price - (data?.Price * data?.Discount) / 100,
                )}
                đ
              </Text>
              <Text style={styles.originalPrice}>
                {Ultis.money(data?.Price ?? 0)} đ
              </Text>
              {/* show discount percent */}
              <View style={styles.discountBadge}>
                <Text style={styles.discountText}>{data?.Discount}%</Text>
              </View>
            </View>
          ) : (
            <Text style={styles.priceText}>{Ultis.money(data?.Price)} đ</Text>
          )}
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.favoriteButton}
              onPress={() => {
                if (data) {
                  dispatch(
                    updateFavoriteProduct({...data, IsFavorite: !like} as any),
                  );
                  setLike(!like);
                }
              }}>
              <Winicon
                src={
                  like
                    ? 'fill/user interface/heart'
                    : 'outline/user interface/heart'
                }
                size={20}
                color={like ? '#FF0000' : '#000'}
              />
            </TouchableOpacity>
            <TouchableOpacity style={styles.shareButton}>
              <Winicon src="fill/user interface/share" size={20} color="#000" />
            </TouchableOpacity>
          </View>
        </View>

        {/* Product Title */}
        <Text style={styles.productTitle}>{data?.Name || ''}</Text>

        {/* Product Details */}
        <View style={styles.detailsContainer}>
          <View style={styles.detailsHorizontalContainer}>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>
                Hãng: {data?.BrandName || ''}
              </Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Đã bán: {data?.Sold || 0}</Text>
            </View>
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Còn: {data?.InStock || 0}</Text>
            </View>
          </View>
        </View>

        {/* Shipping */}
        <View style={styles.shippingContainer}>
          <AppSvg SvgSrc={iconSvg.delivery} size={20} />
          {data?.IsFreeShip ? (
            <Text style={styles.shippingFree}>Miễn phí giao hàng</Text>
          ) : (
            <Text style={styles.shippingDistance}>3.0km Free</Text>
          )}
        </View>

        {/* Rating */}
        <View style={styles.ratingContainer}>
          <Rating
            value={data?.rating ? Number(data.rating) : 0}
            size={20}
            fillColor="#FFC043"
          />
          <Text style={styles.ratingText}>
            {data?.rating ? Number(data.rating).toFixed(1) : '0.0'}
          </Text>
          <View style={{flex: 1}} />
          <TouchableOpacity
            onPress={() => {
              navigation.navigate(RootScreen.RatingScreen, {id: data?.Id});
            }}>
            <Text style={styles.viewAllText}>Xem tất cả</Text>
          </TouchableOpacity>
        </View>

        {/* Seller Info */}
        <TouchableOpacity
          onPress={() => {
            if (shop)
              navigation.navigate(RootScreen.InforShopView, {shop: shop});
          }}
          style={styles.sellerContainer}>
          <FastImage
            source={{
              uri:
                shop?.Img != '' || shop?.Img != null
                  ? shop?.Img
                  : 'https://placehold.co/48/000000/FFFFFF', // Default color is black
            }}
            style={styles.sellerAvatar}
          />
          <View style={styles.sellerInfo}>
            <Text style={styles.sellerName}>{shop?.Name || '-'}</Text>
            <View style={styles.sellerMeta}>
              {/* <View style={styles.sellerBadge}> */}
              {/* <AppSvg SvgSrc={iconSvg.star} size={14} />
                <Text style={styles.sellerBadgeText}>
                  {shop?.rating?.toFixed(1) ?? 0}
                </Text> */}
              {/* </View> */}
              <Text style={styles.sellerContact}>
                {shop?.Mobile
                  ? shop.Mobile?.replace(
                      /(\d{3})(\d+)(\d{4})/g,
                      (_: any, p1: string, p2: string | any[], p3: string) =>
                        `${p1}${'*'.repeat(p2.length)}${p3}`,
                    )
                  : '-'}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
        {/* infor shop */}
        <View
          style={{
            paddingHorizontal: 24,
            flexDirection: 'row',
            alignContent: 'center',
            justifyContent: 'space-between',
            borderBottomWidth: 1,
            borderBottomColor: ColorThemes.light.neutral_main_border_color,
            paddingBottom: 12,
          }}>
          <View
            style={{
              gap: 2,
              alignItems: 'center',
            }}>
            <Text style={{...TypoSkin.body3}}>
              {shop?.rating?.toFixed(1) ?? '-'}
            </Text>
            <Text style={{...TypoSkin.body3}}>Đánh giá</Text>
          </View>
          {/* add divider */}
          <View
            style={{
              width: 1,
              height: '100%',
              backgroundColor: ColorThemes.light.neutral_main_border_color,
            }}
          />
          <View
            style={{
              gap: 2,
              alignItems: 'center',
            }}>
            <Text style={{...TypoSkin.body3}}>
              {shop?.totalProducts ?? '-'}
            </Text>
            <Text style={{...TypoSkin.body3}}>Sản phẩm</Text>
          </View>
          <View
            style={{
              width: 1,
              height: '100%',
              backgroundColor: ColorThemes.light.neutral_main_border_color,
            }}
          />
          <View style={{gap: 2, alignItems: 'center'}}>
            <Text style={{...TypoSkin.body3}}>{shop?.totalOrder ?? '-'}</Text>
            <Text style={{...TypoSkin.body3}}>Đã bán</Text>
          </View>
        </View>

        {/* products of shop */}
        {shop?.products && shop.products.length > 0 && (
          <View
            style={{
              paddingHorizontal: 16,
              paddingTop: 24,
            }}>
            <Text style={{...TypoSkin.semibold3, marginBottom: 12}}>
              Sản phẩm khác của shop
            </Text>
            <FlatList
              data={shop?.products}
              horizontal={true}
              snapToInterval={ITEM_WIDTH + ITEM_SPACING}
              snapToAlignment="start"
              decelerationRate="fast"
              pagingEnabled={false}
              disableIntervalMomentum={true}
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{gap: ITEM_SPACING, paddingVertical: 12}}
              keyExtractor={keyExtractor}
              renderItem={renderProductItem}
              getItemLayout={getItemLayout}
              // Performance optimizations
              removeClippedSubviews={true}
              maxToRenderPerBatch={5}
              updateCellsBatchingPeriod={50}
              initialNumToRender={3}
              windowSize={5}
              legacyImplementation={false}
            />
          </View>
        )}

        {/* Product Description */}
        {data?.Content && (
          <View style={styles.descriptionContainer}>
            <Text style={{...TypoSkin.semibold3, marginBottom: 12}}>
              Chi tiết sản phẩm
            </Text>
            <RenderHTML
              contentWidth={windowWidth}
              source={{html: data?.Content}}
              tagsStyles={{
                body: {
                  color: '#313135',
                  fontSize: 14,
                  lineHeight: 20,
                  fontFamily: 'Inter',
                },
              }}
              ignoredDomTags={['font']} // Thêm dòng này để bỏ qua thẻ font
            />
          </View>
        )}

        {/* sản phẩm gợi ý */}
        <SuggestionProductSection
          onSeeAllPress={() => {
            navigation.navigate(RootScreen.ProductListByCategory, {
              categoryId: data?.CategoryId,
              categoryName: data?.CategoryName,
            });
          }}
          onRefresh={refreshing}
        />
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity style={styles.chatButton}>
          <View style={styles.actionButtonContent}>
            <Winicon src="fill/user interface/f-chat" size={24} color="#fff" />
            <Text style={styles.actionButtonText}>Chat ngay</Text>
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.cartAddButton}
          onPress={() => {
            dispatch(CartActions.addItemToCart(data, 1));
          }}>
          <View style={styles.actionButtonContent}>
            <Winicon src="fill/shopping/cart" size={24} color="#fff" />
            <Text style={styles.actionButtonText}>Thêm vào giỏ hàng</Text>
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.buyButton}
          onPress={() => {
            if (!customer) {
              dialogCheckAcc(DialogRef);
              return;
            }
            navigation.navigate(RootScreen.CheckoutPage, {
              items: [
                {
                  ...data,
                  ProductId: data?.Id,
                  Quantity: 1,
                  ShopId: shop?.Id,
                  ShopName: shop?.Name,
                  ShopAvatar: shop?.Img,
                },
              ],
              address: customerAddress?.find((item: any) => item.IsDefault),
            });
          }}>
          <View style={styles.actionButtonContent}>
            <Winicon src="fill/shopping/box-ribbon" size={24} color="#fff" />
            <Text style={styles.actionButtonText}>Mua ngay</Text>
          </View>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'transparent',
    position: 'absolute',
    top: 30,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  cartButton: {
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#FF4D4F',
    borderRadius: 10,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartBadgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  imageContainer: {
    width: '100%',
    height: 350,
    position: 'relative',
    backgroundColor: ColorThemes.light.neutral_main_background_color,
  },
  productImage: {
    width: Dimensions.get('window').width,
    height: '100%',
  },
  imageCounter: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  imageCounterText: {
    color: '#fff',
    fontSize: 12,
  },
  // Thumbnail gallery styles
  thumbnailContainer: {
    padding: 16,
    paddingTop: 12,
    backgroundColor: '#fff',
  },
  thumbnailList: {
    marginTop: 8,
  },
  thumbnailItem: {
    width: 61,
    height: 61,
    borderRadius: 4,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    overflow: 'hidden',
  },
  selectedThumbnail: {
    borderColor: '#FF4D4F',
    borderWidth: 2,
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  // Variants section
  variantsContainer: {
    padding: 16,
    paddingTop: 0,
  },
  variantsTitle: {
    fontSize: 14,
    fontWeight: '400',
    marginBottom: 12,
    color: '#000',
    lineHeight: 22,
  },
  variantsList: {
    gap: 8,
  },
  variantItem: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    overflow: 'hidden',
    marginRight: 8,
  },
  selectedVariant: {
    borderColor: ColorThemes.light.primary_main_color,
    borderWidth: 2,
  },
  variantImage: {
    width: 60,
    height: 60,
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 0.3,
    borderBottomColor: '#90C8FB',
    // paddingVertical: 8,
    marginTop: 12,
  },
  priceText: {
    ...TypoSkin.title2,
    color: '#FF4D4F',
  },
  originalPrice: {
    ...TypoSkin.title5,
    color: '#999',
    textDecorationLine: 'line-through',
  },
  discountBadge: {
    backgroundColor: '#FFA500',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  discountText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  favoriteButton: {},
  shareButton: {},
  productTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
    paddingHorizontal: 16,
    paddingVertical: 8,
    lineHeight: 24,
    backgroundColor: '#fff',
    borderBottomWidth: 0.3,
    borderBottomColor: '#90C8FB',
  },
  detailsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  detailsHorizontalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    flex: 1,
  },
  detailItem: {
    flexDirection: 'column',
    alignItems: 'center',
    paddingVertical: 4,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  detailLabel: {
    ...TypoSkin.body3,
    fontWeight: '400',
  },
  shippingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  shippingDistance: {
    fontSize: 12,
    color: '#3FB993',
    marginLeft: 8,
  },
  shippingFree: {
    fontSize: 13,
    color: '#3FB993',
    fontWeight: '500',
    marginLeft: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  starsContainer: {
    flexDirection: 'row',
  },
  ratingText: {
    fontSize: 14,
    color: '#000',
    marginLeft: 8,
  },
  viewAllText: {
    fontSize: 14,
    color: ColorThemes.light.primary_main_color,
    marginLeft: 'auto',
  },
  sellerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  sellerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F0F0',
  },
  sellerInfo: {
    marginLeft: 12,
  },
  sellerName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000',
  },
  sellerMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  sellerBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  sellerBadgeText: {
    color: '#8C8C8C',
    fontSize: 12,
    marginLeft: 4,
  },
  sellerContact: {
    fontSize: 12,
    color: '#666',
  },
  descriptionContainer: {
    padding: 16,
  },
  bottomActions: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    height: 64,
  },
  chatButton: {
    flex: 1,
    backgroundColor: '#1877F2',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartAddButton: {
    flex: 1,
    backgroundColor: '#0066CC',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buyButton: {
    flex: 1,
    backgroundColor: '#FF4D4F',
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtonContent: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 4,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
});
