import {StyleSheet, View} from 'react-native';
import RenderHTML from 'react-native-render-html';
import {useWindowDimensions} from 'react-native';

const Content = ({data}: {data: any}) => {
  const {width} = useWindowDimensions();

  return (
    <View style={styles.container}>
      <RenderHTML
        contentWidth={width}
        source={{html: data}}
        tagsStyles={{
          body: {
            color: '#313135',
            fontSize: 14,
            lineHeight: 20,
            fontFamily: 'Inter',
          },
        }}
        ignoredDomTags={['font']}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
  },
});

export default Content;
