import React, {useMemo, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  StatusBar,
  Platform,
  ActivityIndicator,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {
  Winicon,
  ComponentStatus,
  showSnackbar,
  AppSvg,
  showBottomSheet,
  FBottomSheet,
  ListTile,
  hideBottomSheet,
  FDialog,
  showDialog,
} from 'wini-mobile-components';
import {TypoSkin} from '../../assets/skin/typography';
import FastImage from 'react-native-fast-image';
import ConfigAPI from '../../Config/ConfigAPI';
import {CartItem} from '../../redux/types/cartTypes';
import {randomGID, Ultis} from '../../utils/Utils';
import iconSvg from '../../svg/icon';
import {OrderDA} from '../../modules/order/orderDA';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {useCartActions} from '../../redux/hook/cartHook';
import {MissionType, StatusOrder} from '../../Config/Contanst';
import {navigate, navigateReset, RootScreen} from '../../router/router';
import {RenderHeaderCart, RenderRecipientInfo} from './CartPage';
import {SafeAreaView} from 'react-native-safe-area-context';
import {ColorThemes} from '../../assets/skin/colors';
import {Radio, RadioAction} from '../../components/Field/Radio';
import {useDispatch} from 'react-redux';
import {CustomerActions} from '../../redux/reducers/CustomerReducer';
import {dialogCheckAcc} from '../Layout/mainLayout';
import WalletDA from '../../modules/wallet/da';
import WScreenFooter from '../Layout/footer';
import store from '../../redux/store/store';

const {width} = Dimensions.get('window');
const STATUSBAR_HEIGHT = StatusBar.currentHeight || 0;

interface CheckoutRouteParams {
  items: CartItem[];
  address: any;
}

interface StoreGroup {
  ShopId: string;
  ShopName: string;
  ShopAvatar: string;
  items: CartItem[];
  totalPrice: number;
}
export const paymentData = [
  {id: 1, name: 'Ship COD'},
  {id: 2, name: 'VNPay'},
];

const CheckoutPage: React.FC = () => {
  const navigation = useNavigation<any>();
  const route = useRoute();
  const cartActions = useCartActions();
  const [isProcessing, setIsProcessing] = useState(false);
  const [_orderSuccess, setOrderSuccess] = useState(false);
  const [payment, setPayment] = useState(paymentData[0]);

  const btsRef = React.useRef<any>(null);
  const orderDA = new OrderDA();
  const dispatch = useDispatch<any>();

  const [isDone, setDone] = useState(false);

  // Lấy dữ liệu từ route params
  const {items = [], address = null} =
    (route.params as CheckoutRouteParams) || {};
  const customer = useSelectorCustomerState().data;

  const customerAdress = useSelectorCustomerState().myAddress;

  React.useEffect(() => {
    if (customer) dispatch(CustomerActions.getAddresses(customer.Id));
  }, []);

  // Nhóm các sản phẩm theo cửa hàng
  const [storeGroups, setStoreGroups] = useState<StoreGroup[]>([]);
  const [loadingStoreGroups, setLoadingStoreGroups] = useState(false);

  React.useEffect(() => {
    const processItems = async () => {
      if (items.length === 0) {
        setStoreGroups([]);
        return;
      }

      setLoadingStoreGroups(true);
      const storeMap = new Map<string, StoreGroup>();

      try {
        // Sử dụng Promise.all để xử lý tất cả items song song
        await Promise.all(
          items.map(async item => {
            if (!storeMap.has(item.ShopId)) {
              storeMap.set(item.ShopId, {
                ShopId: item.ShopId,
                ShopName: item.ShopName,
                ShopAvatar: item.ShopAvatar,
                items: [],
                totalPrice: 0,
              });
            }

            const group = storeMap.get(item.ShopId)!;

            //#region tính số tiền được hoàn
            const walletDA = new WalletDA();
            const cal = await walletDA.getPercentRewardByShop(item.ShopId, [
              item.CategoryId,
            ]);
            const rewardItem = cal?.find(
              (rewardItem: any) => rewardItem.categoryId === item.CategoryId,
            ) as any;
            const percent = rewardItem?.f0 || 0;
            const reward =
              (item.Price *
                item.Quantity *
                (1 - (item.Discount ?? 0) / 100) *
                percent) /
              100;
            //#endregion
            group.items.push({...item, reward: reward});
            group.totalPrice +=
              item.Price * item.Quantity * (1 - (item.Discount ?? 0) / 100);
          }),
        );

        setStoreGroups(Array.from(storeMap.values()));
      } catch (error) {
        console.error('Error processing store groups:', error);
        setStoreGroups([]);
      } finally {
        setLoadingStoreGroups(false);
      }
    };

    processItems();
  }, [items]);

  // Tính tổng tiền của tất cả sản phẩm
  const totalPrice = React.useMemo(() => {
    return storeGroups.reduce((sum, group) => sum + group.totalPrice, 0);
  }, [storeGroups]);

  const dialogRef = React.useRef<any>(null);

  const submitOder = () => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.SUCCSESS,
      title: 'Bạn chắc chắn muốn đặt hàng?',
      onSubmit: async () => {
        await handlePlaceOrder();
      },
    });
  };

  // Xử lý khi nhấn nút đặt hàng
  const handlePlaceOrder = async () => {
    if (isProcessing) {
      return;
    }
    if (!customer) {
      dialogCheckAcc(dialogRef);
      showSnackbar({
        message: 'Vui lòng đăng nhập để tiếp tục thanh toán',
        status: ComponentStatus.WARNING,
      });
      return;
    }
    if (customerAdress?.length == 0) {
      showSnackbar({
        message: 'Vui lòng cập nhật điểm giao hàng để tiếp tục thanh toán',
        status: ComponentStatus.WARNING,
      });
      return;
    }
    setIsProcessing(true);

    if (storeGroups.length > 0) {
      // Lưu danh sách ID của các sản phẩm đã đặt hàng thành công
      var successfullyOrderedItemIds: any[] = [];
      var listOrder: any[] = [];
      var listOrderDetail: any[] = [];
      for (const group of storeGroups) {
        const order = {
          Id: randomGID(),
          CustomerId: customer?.Id,
          Name: customer?.Name,
          ShopId: group.ShopId,
          Code: Ultis.randomString(10).toLocaleUpperCase(),
          DateCreated: new Date().getTime(),
          DateUpdated: new Date().getTime(),
          Status: StatusOrder.new,
          Value: group.totalPrice,
          AddressId:
            address?.Id || customerAdress?.find(item => item.IsDefault)?.Id,
          PaymentType: payment.id,
          Description: 'Giao hàng nhanh',
        };
        listOrder.push(order);
        for (const item of group.items) {
          var price = parseFloat(item.Price?.toString() ?? '0');
          var discount = parseFloat(item.Discount?.toString() ?? '0');
          const orderDetail = {
            Id: randomGID(),
            Name: item.Name,
            OrderId: order.Id,
            ProductId: item.ProductId,
            DateCreated: new Date().getTime(),
            Quantity: item.Quantity ?? 1,
            Price: price,
            Discount: discount,
            Status: StatusOrder.new,
            Total: price * item.Quantity * (1 - (discount ?? 0) / 100),
          };
          successfullyOrderedItemIds.push(item.id);
          listOrderDetail.push(orderDetail);
        }
      }
      const detailRes = await orderDA.createOrderDetail(listOrderDetail);
      const res = await orderDA.createOrder(listOrder);
      if (detailRes?.code === 200 && res?.code === 200) {
        // Lưu danh sách ID của các sản phẩm đã đặt hàng thành công
        if (successfullyOrderedItemIds.length > 0) {
          cartActions.removeItemsById(successfullyOrderedItemIds);
          setOrderSuccess(true);

          //làm nhiệm vụ đơn hàng bất kỳ.
          const rankInfo = store.getState().customer.rankInfo;
          const walletDA = new WalletDA();
          await walletDA.CaculateMisson(
            customer.Id,
            MissionType.Order,
            rankInfo,
          );
          showSnackbar({
            message: 'Đặt hàng thành công!',
            status: ComponentStatus.SUCCSESS,
          });
          setDone(true);
          setIsProcessing(false);
          if (storeGroups.length > 0) {
          } else {
          }
        }
      } else {
        setIsProcessing(false);
        showSnackbar({
          message: 'Đặt hàng thất bại. Vui lòng thử lại sau.',
          status: ComponentStatus.ERROR,
        });
      }
    }
    // } catch (error) {
    //   console.error('Error placing order:', error);
    //   showSnackbar({
    //     message: 'Đặt hàng thất bại. Vui lòng thử lại sau.',
    //     status: ComponentStatus.ERROR,
    //   });
    // } finally {
    // setIsProcessing(false);
    // }
  };

  // Render một sản phẩm
  const renderProductItem = (item: CartItem) => {
    return (
      <View style={styles.productItemContainer} key={item.id}>
        <FastImage
          style={styles.productImage}
          source={{
            uri: item.Img?.startsWith('http')
              ? item.Img
              : `${ConfigAPI.urlImg}${item.Img}`,
            priority: FastImage.priority.normal,
          }}
          resizeMode={FastImage.resizeMode.cover}
        />

        <View style={styles.productDetails}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <Text style={{...styles.productName, flex: 1}} numberOfLines={2}>
              {item?.Name ?? ''}
            </Text>
            <Text style={styles.productName}>x{item?.Quantity ?? ''}</Text>
          </View>
          <Text style={{...TypoSkin.body3}}>
            Hoàn tiền: {Ultis.money(item.reward ?? 0)} CANPOINT
          </Text>
          <View style={{flexDirection: 'row', alignItems: 'center', gap: 8}}>
            {item?.Discount && item?.Discount > 0 ? (
              <View
                style={{flexDirection: 'row', gap: 8, alignItems: 'center'}}>
                <Text
                  style={{
                    ...styles.productPrice,
                    color: '#000',
                    textDecorationLine: 'line-through',
                  }}>
                  {Ultis.money(item.Price ?? 0)} đ
                </Text>
                <Text style={styles.productPrice}>
                  {Ultis.money(item.Price - (item.Price * item.Discount) / 100)}
                </Text>
              </View>
            ) : (
              <Text style={styles.productPrice}>{Ultis.money(item.Price)}</Text>
            )}
          </View>
        </View>
      </View>
    );
  };

  // Render một nhóm cửa hàng
  const renderStoreGroup = (storeGroup: StoreGroup, index: number) => {
    return (
      <View style={styles.storeContainer} key={storeGroup.ShopId + index}>
        {isDone ? null : (
          <View style={styles.storeHeader}>
            <FastImage
              style={styles.storeAvatar}
              source={{
                uri: storeGroup.ShopAvatar?.startsWith('http')
                  ? storeGroup.ShopAvatar
                  : `${ConfigAPI.urlImg}${storeGroup.ShopAvatar}`,
                priority: FastImage.priority.normal,
              }}
              resizeMode={FastImage.resizeMode.cover}
            />

            <Text style={styles.storeName}>{storeGroup?.ShopName ?? ''}</Text>
          </View>
        )}

        {storeGroup.items.map(item => renderProductItem(item))}

        <View style={styles.shippingContainer}>
          {storeGroup.items?.find(item => item?.IsFreeShip) ? (
            <View style={styles.shippingRow}>
              <AppSvg SvgSrc={iconSvg.delivery} size={20} />
              <Text style={styles.freeShippingText}>Free</Text>
            </View>
          ) : (
            <View style={styles.shippingRow}></View>
          )}

          <View style={styles.paymentRow}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <AppSvg SvgSrc={iconSvg.money} size={20} />
              <Text style={styles.paymentLabel}>Phương thức thanh toán:</Text>
              <Text style={styles.paymentMethod}>{payment.name}</Text>
            </View>
            {isDone ? null : (
              <TouchableOpacity
                style={{padding: 4}}
                onPress={() => {
                  showBottomSheet({
                    ref: btsRef,
                    title: 'Phương thức thanh toán',
                    enableDismiss: true,
                    dismiss: () => {
                      hideBottomSheet(btsRef);
                    },
                    children: (
                      <View
                        key={`payment-list-${payment.id}`}
                        style={{
                          height: 200,
                          width: '100%',
                          backgroundColor:
                            ColorThemes.light.neutral_absolute_background_color,
                        }}>
                        {paymentData.map((item, index) => {
                          const isSelected = payment.id === item.id;

                          return (
                            <ListTile
                              key={index}
                              title={item.name}
                              trailing={
                                <TouchableOpacity
                                  onPress={() => {
                                    setPayment(item);
                                    hideBottomSheet(btsRef);
                                  }}>
                                  {isSelected ? <RadioAction /> : <Radio />}
                                </TouchableOpacity>
                              }
                              onPress={() => {
                                setPayment(item);
                                hideBottomSheet(btsRef);
                              }}
                            />
                          );
                        })}
                      </View>
                    ),
                  });
                }}>
                <Text
                  style={{
                    ...TypoSkin.body3,
                    color: ColorThemes.light.infor_main_color,
                  }}>
                  Đổi PTTT
                </Text>
              </TouchableOpacity>
            )}
          </View>

          <View
            style={{
              ...styles.storeTotalRow,
              borderTopWidth: 1,
              borderTopColor: ColorThemes.light.neutral_main_border_color,
            }}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <AppSvg SvgSrc={iconSvg.moneyGold} size={20} />
              <Text style={styles.storeTotalLabel}>
                CAN POINT được hoàn ({storeGroup.items.length} SP):
              </Text>
            </View>
            <Text style={{...styles.storeTotalPrice, color: '#3FB993'}}>
              {Ultis.money(
                storeGroup.items.reduce(
                  (sum, item) => sum + (item.reward || 0),
                  0,
                ),
              )}{' '}
              đ
            </Text>
          </View>
          <View style={styles.storeTotalRow}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <AppSvg SvgSrc={iconSvg.moneyGold} size={20} />
              <Text style={styles.storeTotalLabel}>
                Tổng tiền ({storeGroup.items.length} sản phẩm):
              </Text>
            </View>
            <Text style={styles.storeTotalPrice}>
              {Ultis.money(storeGroup.totalPrice ?? 0)} đ
            </Text>
          </View>
        </View>
      </View>
    );
  };

  // Render bottom bar với nút đặt hàng
  const renderBottomBar = () => {
    if (isDone)
      return (
        <WScreenFooter
          style={{
            width: '100%',
            gap: 8,
            alignItems: 'center',
            paddingHorizontal: 16,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              gap: 8,
              flex: 1,
            }}>
            <TouchableOpacity
              style={[
                styles.placeOrderButton,
                {
                  backgroundColor:
                    ColorThemes.light.neutral_main_background_color,
                  flex: 1,
                  justifyContent: 'center',
                },
              ]}
              onPress={() => {
                navigation.replace(RootScreen.navigateEComView);
              }}>
              <Text
                style={{
                  ...styles.placeOrderButtonText,
                  color: ColorThemes.light.neutral_text_title_color,
                  textAlign: 'center',
                }}>
                Về trang chủ
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.placeOrderButton, {flex: 1}]}
              onPress={() => {
                navigation.replace(RootScreen.OrderCustomerDetail, {
                  status: StatusOrder.new,
                });
              }}>
              <Text
                style={{...styles.placeOrderButtonText, textAlign: 'center'}}>
                Danh sách đơn
              </Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            style={[
              styles.placeOrderButton,
              {
                flex: 1,
                width: '100%',
                backgroundColor: ColorThemes.light.primary_main_color,
              },
            ]}
            onPress={() => {
              navigateReset(RootScreen.navigateEComView, {
                screen: 'products',
              });
            }}>
            <Text
              style={{
                ...styles.placeOrderButtonText,
                flex: 1,
                textAlign: 'center',
              }}>
              Tiếp tục mua hàng
            </Text>
          </TouchableOpacity>
        </WScreenFooter>
      );
    return (
      <View style={styles.bottomBar}>
        <View style={styles.totalContainer}>
          <Text style={styles.totalLabel}>Tổng cộng:</Text>
          <Text style={styles.totalPrice}>{Ultis.money(totalPrice ?? 0)}</Text>
        </View>

        <TouchableOpacity
          style={[
            styles.placeOrderButton,
            isProcessing ? styles.placeOrderButtonDisabled : {},
          ]}
          onPress={submitOder}
          disabled={isProcessing}>
          <Text style={styles.placeOrderButtonText}>Đặt hàng</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      <FBottomSheet ref={btsRef} />
      <FDialog ref={dialogRef} />
      <RenderHeaderCart
        title={isDone ? 'Đơn hàng' : 'Thanh toán và đặt hàng'}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}>
        {isDone ? (
          <View style={{paddingVertical: 16}}>
            <Text
              style={{
                ...TypoSkin.heading5,
                color: ColorThemes.light.success_main_color,
                textAlign: 'center',
              }}>
              Đơn hàng của bạn đã được đặt thành công!
            </Text>
          </View>
        ) : null}
        <RenderRecipientInfo dialogRef={dialogRef} isDone={isDone} />

        {loadingStoreGroups ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#2962FF" />
            <Text style={styles.loadingText}>Đang tính toán...</Text>
          </View>
        ) : (
          storeGroups.map((group, index) => renderStoreGroup(group, index))
        )}

        <View style={styles.bottomSpacer} />
      </ScrollView>

      {renderBottomBar()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    width: '100%',
    height: 140,
    position: 'relative',
  },
  headerBackground: {
    backgroundColor: '#2962FF',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  waveContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 2,
    overflow: 'hidden',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: Platform.OS === 'ios' ? 20 : STATUSBAR_HEIGHT + 10,
    height: 56,
    position: 'relative',
    zIndex: 3,
    left: 0,
    right: 0,
  },
  backButton: {
    position: 'absolute',
    left: 16,
    padding: 8,
  },
  headerTitle: {
    ...TypoSkin.heading5,
    fontSize: 20,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingHorizontal: 16,
  },
  recipientInfoContainer: {
    backgroundColor: '#E6F7FF',
    borderRadius: 10,
    padding: 16,
    marginTop: 12,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#ccc',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  recipientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  recipientTitle: {
    ...TypoSkin.heading6,
    color: '#000000',
  },
  editButton: {
    padding: 4,
  },
  recipientDetails: {
    gap: 4,
  },
  recipientName: {
    ...TypoSkin.body2,
    color: '#000000',
  },
  recipientAddress: {
    ...TypoSkin.body3,
    color: '#666666',
  },
  storeContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  storeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  storeAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  storeName: {
    ...TypoSkin.heading7,
    color: '#000000',
  },
  productItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 12,
  },
  productDetails: {
    flex: 1,
    // justifyContent: 'space-between',
    // alignItems: 'center',
  },
  productName: {
    ...TypoSkin.title3,
    marginBottom: 4,
    paddingRight: 4,
  },
  productVariant: {
    ...TypoSkin.body3,
    color: '#666666',
  },
  productPrice: {
    ...TypoSkin.heading7,
    color: 'red',
    fontWeight: '700',
  },
  shippingContainer: {
    marginTop: 8,
  },
  shippingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  freeShippingText: {
    ...TypoSkin.body2,
    color: '#3FB993',
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '700',
  },
  paymentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  paymentLabel: {
    ...TypoSkin.body2,
    color: '#000000',
    marginLeft: 8,
    marginRight: 4,
    fontSize: 12,
    fontWeight: '700',
  },
  paymentMethod: {
    ...TypoSkin.body2,
    color: '#3FB993',
    lineHeight: 22,
    fontSize: 14,
    fontWeight: '700',
  },
  storeTotalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 16,
  },
  storeTotalLabel: {
    ...TypoSkin.body2,
    color: '#000000',
    fontSize: 12,
    fontWeight: '700',
  },
  storeTotalPrice: {
    ...TypoSkin.heading7,
    fontWeight: '700',
    color: '#FF3B30',
  },
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  totalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalLabel: {
    ...TypoSkin.heading6,
    color: '#000000',
    marginRight: 8,
  },
  totalPrice: {
    ...TypoSkin.heading6,
    color: '#FF3B30',
  },
  placeOrderButton: {
    backgroundColor: '#FFC043',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 40,
  },
  placeOrderButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  placeOrderButtonText: {
    ...TypoSkin.heading7,
    color: '#FFFFFF',
  },
  bottomSpacer: {
    height: 100,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    ...TypoSkin.body2,
    color: '#666666',
    marginTop: 12,
  },
});

export default CheckoutPage;
