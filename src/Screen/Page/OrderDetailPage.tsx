import React, {useEffect, useState} from 'react';
import {
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  StatusBar,
  Platform,
  RefreshControl,
  View,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {
  AppSvg,
  ComponentStatus,
  FDialog,
  showDialog,
  showSnackbar,
  Winicon,
} from 'wini-mobile-components';
import {TypoSkin} from '../../assets/skin/typography';
import FastImage from 'react-native-fast-image';
import {Ultis} from '../../utils/Utils';
import {OrderDA} from '../../modules/order/orderDA';
import {StatusOrder} from '../../Config/Contanst';
import {ColorThemes} from '../../assets/skin/colors';
import ConfigAPI from '../../Config/ConfigAPI';
import {paymentData} from './CheckoutPage';
import {InforHeader} from '../Layout/headers/inforHeader';
import {RootScreen} from '../../router/router';
import Clipboard from '@react-native-clipboard/clipboard';
import iconSvg from '../../svg/icon';
import PopupCancelOrder from '../../components/Popup/PopupCancelOrder';
import PopupUpdateStatusOrder from '../../components/Popup/PopupUpdateStatusOrder';

const {width} = Dimensions.get('window');
const STATUSBAR_HEIGHT = StatusBar.currentHeight || 0;

interface OrderDetailRouteParams {
  orderId: string;
  type?: string;
  CancelReason?: string;
  refundInfo?: any;
}

const OrderDetailPage: React.FC = () => {
  const navigation = useNavigation<any>();
  const route = useRoute();
  const [loading, setLoading] = useState(true);
  const [order, setOrder] = useState<any>(null);
  const [orderDetails, setOrderDetails] = useState<Array<any>>([]);
  const orderDA = new OrderDA();
  const [refreshing, setRefreshing] = useState(false);
  const [isCancelPopupVisible, setCancelPopupVisible] = useState(false);
  const [isSubmittingCancel, setSubmittingCancel] = useState(false);
  const [isUpdateStatusPopupVisible, setUpdateStatusPopupVisible] =
    useState(false);
  const [isSubmittingUpdateStatus, setSubmittingUpdateStatus] = useState(false);

  const dialogRef = React.useRef<any>(null);

  // Lấy orderId từ route params
  const {orderId, type, CancelReason, refundInfo} =
    (route.params as OrderDetailRouteParams) || {};

  // Lấy thông tin đơn hàng và chi tiết đơn hàng
  useEffect(() => {
    fetchOrderData();
  }, [orderId]);
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await fetchOrderData();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };
  const fetchOrderData = async () => {
    if (!orderId) {
      setLoading(false);
      return;
    }

    try {
      // Lấy thông tin đơn hàng
      const orderResponse = await orderDA.getOrderByOrderId(orderId);
      if (orderResponse?.code === 200 && orderResponse.data?.length > 0) {
        orderResponse.data[0].Address = orderResponse.Address.find(
          (item: any) => item.Id === orderResponse.data[0].AddressId,
        );

        console.log('Fetched order data:', orderResponse.data[0]);
        setOrder(orderResponse.data[0]);
        // Lấy chi tiết đơn hàng
        const detailsResponse = await orderDA.getOrderDetailsByOrderId(orderId);
        if (detailsResponse?.code === 200) {
          const listOrderDetailId = detailsResponse.data.map(
            (item: any) => item.Id,
          );
          const historyRewardResponse =
            await orderDA.getMoneyDetailsByListOrderDetailId(listOrderDetailId);
          detailsResponse.data = detailsResponse.data.map((item: any) => {
            var product = detailsResponse.Product.find(
              (product: any) => product.Id === item.ProductId,
            );
            item.Img = product?.Img;
            item.Name = product?.Name;
            item.CategoryId = product?.CategoryId;
            item.Reward = historyRewardResponse?.data.find(
              (history: any) =>
                history.OrderDetailId === item.Id && history.Filial === 0,
            )?.Value;
            return item;
          });
          setOrderDetails(detailsResponse.data);
        }
      }
    } catch (error) {
      console.error('Error fetching order data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelOrder = async () => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.WARNING,
      title: 'Bạn chắc chắn muốn hủy đơn hàng này?',
      onSubmit: async () => {
        try {
          const res = await orderDA.cancelOrder({
            Id: order.Id,
            CustomerId: order.CustomerId,
            ShopId: order.ShopId,
            Code: order.Code,
            DateUpdated: new Date().getTime(),
            Status: StatusOrder.cancel,
            IsCustomer: true,
          });
          if (res?.code === 200) {
            showSnackbar({
              message: 'Hủy đơn hàng thành công!',
              status: ComponentStatus.SUCCSESS,
            });
            navigation.goBack();
          } else {
            showSnackbar({
              message: 'Hủy đơn hàng thất bại!',
              status: ComponentStatus.ERROR,
            });
          }
        } catch (error) {
          console.error('Error canceling order:', error);
          showSnackbar({
            message: 'Hủy đơn hàng thất bại!',
            status: ComponentStatus.ERROR,
          });
        }
      },
    });
  };

  const handleSubmitRejectOrder = async (reason: string) => {
    console.log('handleSubmitRejectOrder called with reason:', reason);
    setSubmittingCancel(true);
    try {
      const cancelData = {
        Id: order.Id,
        CustomerId: order.CustomerId,
        ShopId: order.ShopId,
        Code: order.Code,
        DateUpdated: new Date().getTime(),
        Status: StatusOrder.cancel,
        IsCustomer: false, // Shop is cancelling
        CancelReason: reason,
      };
      console.log('Sending cancel order data:', cancelData);
      const res = await orderDA.cancelOrder(cancelData);
      if (res?.code === 200) {
        showSnackbar({
          message: 'Đã từ chối/hủy đơn hàng thành công!',
          status: ComponentStatus.SUCCSESS,
        });
        setCancelPopupVisible(false);
        fetchOrderData(); // Refresh data to show updated cancel reason
      } else {
        showSnackbar({
          message: res?.message || 'Hủy đơn hàng thất bại!',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.error('Error rejecting order:', error);
      showSnackbar({
        message: 'Hủy đơn hàng thất bại!',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setSubmittingCancel(false);
    }
  };

  const handleSubmitUpdateStatus = async (item: any, status_str?: string) => {
    console.log('check-item', item);
    console.log('check-status_str', status_str);
    if (!item || !status_str) {
      return;
    }
    setSubmittingUpdateStatus(true);
    try {
      let status: number;
      switch (status_str) {
        case 'completed':
          status = StatusOrder.success;
          break;
        case 'cancelled':
          status = StatusOrder.cancel;
          break;
        case 'processing':
          status = StatusOrder.proccess;
          break;
        case 'Pending':
          status = 5;
          break;
        case 'Delivery':
          status = 6;
          break;
        default:
          const parsedStatus = parseInt(status_str, 10);
          if (isNaN(parsedStatus)) {
            showSnackbar({
              message: `Trạng thái không hợp lệ: ${status_str}`,
              status: ComponentStatus.ERROR,
            });
            return;
          }
          status = parsedStatus;
          break;
      }
      const res = await orderDA.updateStatusOrder(item.Id, status);
      if (res?.code === 200) {
        showSnackbar({
          message: 'Cập nhật trạng thái đơn hàng thành công!',
          status: ComponentStatus.SUCCSESS,
        });
        setUpdateStatusPopupVisible(false);
        fetchOrderData(); // to refresh data
      } else {
        showSnackbar({
          message: res?.message || 'Cập nhật trạng thái thất bại!',
          status: ComponentStatus.ERROR,
        });
      }
    } catch (error) {
      console.error('Error updating order status:', error);
      showSnackbar({
        message: 'Cập nhật trạng thái thất bại!',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setSubmittingUpdateStatus(false);
    }
  };

  const currentStatus = order?.Status || StatusOrder.new;
  console.log('check-currentStatus', currentStatus);

  // Render timeline trạng thái đơn hàng
  const renderOrderStatusTimeline = () => {
    if (currentStatus == StatusOrder.cancel)
      return (
        <View
          style={{...styles.timelineContainer, alignItems: 'center', gap: 8}}>
          <Winicon
            src="fill/location/route-close"
            size={36}
            color={ColorThemes.light.error_main_color}
          />
          <Text style={styles.timelineLabel}>Đơn hàng đã hủy</Text>
        </View>
      );
    return (
      <View style={styles.timelineContainer}>
        {/* Các điểm trạng thái */}
        <View style={styles.timelinePoints}>
          {/* Đặt hàng */}
          <View style={styles.timelinePointWrapper}>
            <View
              style={[
                styles.timelinePoint,
                currentStatus >= StatusOrder.new
                  ? styles.timelinePointActive
                  : {},
              ]}>
              {currentStatus >= StatusOrder.new && (
                <Winicon
                  src="fill/user interface/check"
                  size={12}
                  color="#FFFFFF"
                />
              )}
            </View>
            <Text style={styles.timelineTime}>
              {order?.DateCreated || order?.DateUpdated
                ? new Date(
                    order?.DateUpdated || order?.DateCreated,
                  ).toLocaleTimeString('vi-VN', {
                    hour: '2-digit',
                    minute: '2-digit',
                  })
                : ''}
            </Text>
            <Text style={styles.timelineLabel}>Đặt hàng</Text>
          </View>

          {/* Đường nối */}
          <View
            style={[
              styles.timelineConnector,
              currentStatus >= StatusOrder.proccess
                ? styles.timelineConnectorActive
                : {},
            ]}
          />

          {/* Chờ lấy hàng */}
          <View style={styles.timelinePointWrapper}>
            <View
              style={[
                styles.timelinePoint,
                currentStatus >= StatusOrder.proccess
                  ? styles.timelinePointActive
                  : {},
              ]}>
              {currentStatus >= StatusOrder.proccess && (
                <Winicon
                  src="fill/user interface/check"
                  size={12}
                  color="#FFFFFF"
                />
              )}
            </View>
            <Text style={styles.timelineTime}>
              {order?.DateProcess && currentStatus >= StatusOrder.proccess
                ? new Date(order?.DateProcess).toLocaleTimeString('vi-VN', {
                    hour: '2-digit',
                    minute: '2-digit',
                  })
                : '-'}
            </Text>
            <Text style={styles.timelineLabel}>Chờ lấy hàng</Text>
          </View>

          {/* Đường nối */}
          <View
            style={[
              styles.timelineConnector,
              currentStatus >= StatusOrder.proccess
                ? styles.timelineConnectorActive
                : {},
            ]}
          />

          {/* Đang vận chuyển */}
          <View style={styles.timelinePointWrapper}>
            <View
              style={[
                styles.timelinePoint,
                currentStatus == StatusOrder.success
                  ? styles.timelinePointActive
                  : {},
              ]}>
              {currentStatus == StatusOrder.success && (
                <Winicon
                  src="fill/user interface/check"
                  size={12}
                  color="#FFFFFF"
                />
              )}
            </View>
            <Text style={styles.timelineTime}>
              {order?.DateProcess && currentStatus == StatusOrder.success
                ? new Date(order?.DateProcess).toLocaleTimeString('vi-VN', {
                    hour: '2-digit',
                    minute: '2-digit',
                  })
                : '-'}
            </Text>
            <Text style={styles.timelineLabel}>Hoàn thành</Text>
          </View>
        </View>
      </View>
    );
  };

  // Render thông tin đơn hàng
  const renderOrderInfo = () => {
    return (
      <View style={styles.orderInfoContainer}>
        {currentStatus == StatusOrder.cancel && (
          <View style={styles.orderInfoRow}>
            <View style={styles.orderInfoIcon}>
              <Winicon
                src="outline/user interface/bookmark-delete"
                size={16}
                color="#000000"
              />
            </View>
            <View style={styles.orderInfoContent}>
              <Text style={styles.orderInfoLabel}>
                Lý do hủy đơn:{' '}
                {order?.CancelReason || CancelReason || 'Không có lý do'}
              </Text>
            </View>
          </View>
        )}

        {/* Mã đơn hàng */}
        <View style={styles.orderInfoRow}>
          <View style={styles.orderInfoIcon}>
            <Winicon src="fill/shopping/box" size={16} color="#000000" />
          </View>
          <View style={styles.orderInfoContent}>
            <Text style={styles.orderInfoLabel}>
              Mã đơn hàng: #{order?.Code || ''}
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => {
              Clipboard.setString(order?.Code || '');
              showSnackbar({
                message: 'Đã sao chép mã đơn hàng',
                status: ComponentStatus.SUCCSESS,
              });
            }}
            style={styles.copyButton}>
            <Winicon src="fill/files/document-copy" size={16} color="#2962FF" />
          </TouchableOpacity>
        </View>

        {/* Thời gian */}
        <View style={styles.orderInfoRow}>
          <View style={styles.orderInfoIcon}>
            <Winicon
              src="fill/user interface/clock"
              size={16}
              color="#000000"
            />
          </View>
          <View style={styles.orderInfoContent}>
            <Text style={styles.orderInfoLabel}>
              Thời gian:{' '}
              {order?.DateCreated
                ? Ultis.formatDateTime(order.DateCreated, true)
                : ''}
            </Text>
          </View>
        </View>

        {/* Phương thức thanh toán */}
        <View style={styles.orderInfoRow}>
          <View style={styles.orderInfoIcon}>
            <Winicon src="fill/business/wallet-90" size={16} color="#000000" />
          </View>
          <View style={styles.orderInfoContent}>
            <Text style={styles.orderInfoLabel}>
              PTT:{' '}
              {paymentData.find(item => item.id === order?.PaymentType)?.name}
            </Text>
          </View>
        </View>

        {/* Thông tin người nhận */}
        <View style={styles.orderInfoRow}>
          <View style={styles.orderInfoIcon}>
            <Winicon
              src="fill/shopping/shop-location"
              size={20}
              color="#000000"
            />
          </View>
          <View style={styles.orderInfoContent}>
            <Text style={styles.orderInfoLabel}>
              {order?.Address?.Name} - {order?.Address?.Mobile}
            </Text>
            <Text style={styles.orderInfoValue}>{order?.Address?.Email}</Text>
            <Text style={styles.orderInfoValue}>
              {order?.Address?.Address ?? ''}
            </Text>
          </View>
        </View>
      </View>
    );
  };
  // Render sản phẩm trong đơn hàng
  const renderOrderItems = () => {
    return (
      <View style={styles.orderItemsContainer}>
        {orderDetails && orderDetails?.length > 0
          ? orderDetails?.map((item, index) => (
              <TouchableOpacity
                onPress={() =>
                  navigation.push(RootScreen.ProductDetail, {
                    id: item.ProductId,
                  })
                }
                key={item.Id}
                style={styles.orderItemRow}>
                <FastImage
                  style={styles.productImage}
                  source={{
                    uri: item.Img?.startsWith('http')
                      ? item.Img
                      : `${ConfigAPI.urlImg}${item.Img}`,
                    priority: FastImage.priority.normal,
                  }}
                  resizeMode={FastImage.resizeMode.cover}
                />
                <View style={styles.productDetails}>
                  <Text style={styles.productName}>
                    {item?.Name || 'Sản phẩm'}
                  </Text>
                  {/* {item.Product?.Description ? (
                    <Text style={styles.productVariant}>
                      {item.Product?.Description || ''}
                    </Text>
                  ) : null} */}
                  <View style={styles.productPriceRow}>
                    {item?.Discount ? (
                      <View
                        style={{
                          flexDirection: 'row',
                          gap: 8,
                          alignItems: 'center',
                        }}>
                        <Text style={styles.originalPrice}>
                          {Ultis.money(item.Price ?? 0)} đ
                        </Text>
                        <Text style={styles.productPrice}>
                          {Ultis.money(
                            item.Price - (item.Price * item.Discount) / 100,
                          )}{' '}
                          đ
                        </Text>
                      </View>
                    ) : (
                      <Text style={styles.productPrice}>
                        {Ultis.money(item.Price ?? 0)} đ
                      </Text>
                    )}
                  </View>
                  <Text style={{...TypoSkin.buttonText5}}>
                    Hoàn tiền: {Ultis.money(item.Reward ?? 0)} CANPOINT
                  </Text>
                </View>
              </TouchableOpacity>
            ))
          : null}

        {/* Tổng tiền */}
        {!type ? (
          <>
            <View style={styles.totalContainer}>
              <Text style={styles.totalLabel}>Tổng hoàn CANPOINT:</Text>
              <Text style={{...TypoSkin.heading7, color: '#FF3B30'}}>
                {Ultis.money(
                  orderDetails?.reduce(
                    (total: number, item: any) => total + (item.Reward ?? 0),
                    0,
                  ) ?? 0,
                )}{' '}
                đ
              </Text>
            </View>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingTop: 16,
              }}>
              <Text style={styles.totalLabel}>
                Tổng tiền ({orderDetails?.length ?? 0} sản phẩm):
              </Text>
              <Text style={styles.totalPrice}>
                {Ultis.money(order?.Value ?? 0)} đ
              </Text>
            </View>
          </>
        ) : (
          <View>
            <View style={styles.totalContainer}>
              <Text style={styles.totalLabel}>
                Trả Affiliate ( {refundInfo?.all || 0} sản phẩm):
              </Text>
              <Text style={{...TypoSkin.heading7, color: '#FF3B30'}}>
                {Ultis.money(refundInfo?.allRefund ?? 0)} đ
              </Text>
            </View>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingTop: 16,
              }}>
              <Text style={styles.totalLabel}>
                Tổng tiền ({orderDetails?.length ?? 0} sản phẩm):
              </Text>
              <Text style={styles.totalPrice}>
                {Ultis.money(order?.Value ?? 0)} đ
              </Text>
            </View>
          </View>
        )}
      </View>
    );
  };

  // Render các nút hành động
  const renderActionButtons = (currentStatus: any) => {
    return (
      <View style={styles.actionButtonsContainer}>
        {currentStatus < StatusOrder.proccess && (
          <TouchableOpacity
            onPress={handleCancelOrder}
            disabled={currentStatus >= StatusOrder.proccess}
            style={styles.cancelButton}>
            <Text style={styles.cancelButtonText}>Hủy đơn</Text>
          </TouchableOpacity>
        )}

        <View style={styles.actionButtonsGroup}>
          <TouchableOpacity style={styles.chatButton}>
            <Winicon
              src="outline/user interface/chat"
              size={20}
              color="#000000"
            />
            <Text style={styles.chatButtonText}>Chat với cửa hàng</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.callButton}>
            <Winicon
              src="outline/user interface/phone"
              size={20}
              color="#000000"
            />
            <Text style={styles.callButtonText}>Liên hệ cửa hàng</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <FDialog ref={dialogRef} />
      <PopupCancelOrder
        visible={isCancelPopupVisible}
        onClose={() => setCancelPopupVisible(false)}
        onSubmit={handleSubmitRejectOrder}
        loading={isSubmittingCancel}
      />
      <PopupUpdateStatusOrder
        item={order}
        visible={isUpdateStatusPopupVisible}
        onClose={() => setUpdateStatusPopupVisible(false)}
        handleUpdateStatusProcessOrder={handleSubmitUpdateStatus}
      />
      <RenderHeaderOrder title="Xem chi tiết đơn hàng" />

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[ColorThemes.light.primary_main_color]}
            tintColor={ColorThemes.light.primary_main_color}
          />
        }
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}>
        {renderOrderStatusTimeline()}
        {renderOrderInfo()}
        {renderOrderItems()}
        <View style={styles.bottomSpacer} />
      </ScrollView>
      {renderActionButtons(currentStatus)}
    </View>
  );
};

export const RenderHeaderOrder = ({title = ''}: {title?: string}) => {
  const navigation = useNavigation<any>();
  return <InforHeader title={title} onBack={() => navigation.goBack()} />;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  header: {
    width: '100%',
    height: 140,
    position: 'relative',
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  waveContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 2,
    overflow: 'hidden',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: Platform.OS === 'ios' ? 20 : STATUSBAR_HEIGHT + 10,
    position: 'relative',
    zIndex: 3,
    left: 0,
    right: 0,
  },

  headerActionButton: {
    padding: 8,
    marginLeft: 8,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingBottom: 20,
  },
  timelineContainer: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 20,
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  timelinePoints: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  timelinePointWrapper: {
    alignItems: 'center',
    width: 70,
  },
  timelinePoint: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#E0E0E0',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  timelinePointActive: {
    backgroundColor: '#4CAF50',
  },
  timelineConnector: {
    marginTop: 12,
    height: 1.5,
    backgroundColor: '#E0E0E0',
    flex: 1,
  },
  timelineConnectorActive: {
    backgroundColor: '#4CAF50',
  },
  timelineTime: {
    ...TypoSkin.body3,
    color: '#757575',
    marginBottom: 2,
  },
  timelineLabel: {
    ...TypoSkin.body3,
    color: '#212121',
    textAlign: 'center',
  },
  orderInfoContainer: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 16,
    marginBottom: 8,
  },
  orderInfoRow: {
    flexDirection: 'row',
    padding: 16,
    borderBottomColor: '#EEEEEE',
    borderBottomWidth: 1,
    alignItems: 'center',
  },
  orderInfoIcon: {
    width: 24,
    marginRight: 12,
  },
  orderInfoContent: {
    flex: 1,
  },
  orderInfoLabel: {
    ...TypoSkin.body2,
    color: ColorThemes.light.neutral_text_title_color,
  },
  orderInfoValue: {
    ...TypoSkin.body3,
    color: '#757575',
  },
  copyButton: {
    padding: 4,
  },
  orderItemsContainer: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    marginBottom: 8,
  },
  orderItemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  productDetails: {
    flex: 1,
  },
  productName: {
    ...TypoSkin.body3,
    color: '#212121',
    marginBottom: 4,
  },
  productVariant: {
    ...TypoSkin.body3,
    color: '#757575',
    marginBottom: 4,
  },
  productPriceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  productPrice: {
    ...TypoSkin.heading7,
    color: '#FF3B30',
    marginRight: 8,
  },
  originalPrice: {
    ...TypoSkin.body3,
    color: '#757575',
    textDecorationLine: 'line-through',
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 16,
  },
  totalLabel: {
    ...TypoSkin.body2,
    color: '#212121',
  },
  totalPrice: {
    ...TypoSkin.heading6,
    color: '#FF3B30',
  },
  actionButtonsContainer: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    paddingBottom: 32,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  cancelButton: {
    backgroundColor: '#FF3B30',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  cancelButtonText: {
    ...TypoSkin.heading7,
    color: '#FFFFFF',
  },
  actionButtonsGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  chatButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingVertical: 12,
    marginRight: 8,
  },
  chatButtonText: {
    ...TypoSkin.body2,
    color: '#212121',
    marginLeft: 8,
  },
  callButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingVertical: 12,
    marginLeft: 8,
  },
  callButtonText: {
    ...TypoSkin.body2,
    color: '#212121',
    marginLeft: 8,
  },
  bottomSpacer: {
    height: 100,
  },
});

export default OrderDetailPage;
